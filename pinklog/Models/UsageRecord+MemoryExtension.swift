import Foundation
import SwiftUI
import CoreData

extension UsageRecord {
    // MARK: - 回忆录相关扩展
    
    /// 获取故事封面图片数据
    /// - Returns: 封面图片数据，遵循fallback顺序
    func getStoryCoverImageData() -> Data? {
        // 1. 优先使用故事自身的第一张图片
        if let imagesData = self.images, let imageDataArray = try? NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imagesData) as? [Data], let firstImageData = imageDataArray.first {
            return firstImageData
        }
        
        // 2. 如果故事没有图片，使用关联产品的第一张图片
        if let product = self.product, let productImagesData = product.images, let productImageDataArray = try? NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(productImagesData) as? [Data], let firstProductImageData = productImageDataArray.first {
            return firstProductImageData
        }
        
        // 3. 如果都没有，返回nil，交由UI层处理默认图标显示
        return nil
    }
    
    /// 获取带格式的故事日期
    /// - Parameter showRelative: 是否显示相对日期
    /// - Returns: 格式化的日期字符串
    func getFormattedStoryDate(showRelative: Bool = true) -> String {
        guard let date = self.date else {
            return "未知日期"
        }
        
        let calendar = Calendar.current
        let now = Date()
        
        // 计算日期差异
        let components = calendar.dateComponents([.day], from: date, to: now)
        let dayDifference = components.day ?? 0
        
        if showRelative && dayDifference < 7 {
            if dayDifference == 0 {
                return "今天"
            } else if dayDifference == 1 {
                return "昨天"
            } else {
                return "\(dayDifference)天前"
            }
        } else {
            // 使用标准日期格式
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy年M月d日"
            return formatter.string(from: date)
        }
    }
    
    /// 获取故事摘要
    /// - Parameter maxLength: 最大长度
    /// - Returns: 故事摘要文本
    func getStorySummary(maxLength: Int = 60) -> String {
        // 优先使用标题
        if let title = self.title, !title.isEmpty {
            if title.count <= maxLength {
                return title
            }
            let endIndex = title.index(title.startIndex, offsetBy: maxLength - 3)
            return String(title[..<endIndex]) + "..."
        }
        
        // 如果没有标题，使用笔记内容
        if let notes = self.notes, !notes.isEmpty {
            // 移除Markdown格式
            var plainText = notes
                .replacingOccurrences(of: "#", with: "")
                .replacingOccurrences(of: "\\*\\*(.+?)\\*\\*", with: "$1", options: .regularExpression)
                .replacingOccurrences(of: "\\*(.+?)\\*", with: "$1", options: .regularExpression)
                .replacingOccurrences(of: "\\[(.+?)\\]\\(.+?\\)", with: "$1", options: .regularExpression)
            
            // 截取指定长度
            if plainText.count <= maxLength {
                return plainText
            }
            let endIndex = plainText.index(plainText.startIndex, offsetBy: maxLength - 3)
            return String(plainText[..<endIndex]) + "..."
        }
        
        return "这是一个物品故事..."
    }
    
    /// 获取故事图片数组
    /// - Returns: 图片数据数组
    func getStoryImages() -> [Data] {
        if let imagesData = self.images, let imageDataArray = try? NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(imagesData) as? [Data] {
            return imageDataArray
        }
        return []
    }
    
    /// 检查故事是否有音频
    var hasAudio: Bool {
        guard let audioData = self.audioRecordings else {
            return false
        }
        
        if let urls = try? NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(audioData) as? [URL], !urls.isEmpty {
            return true
        }
        return false
    }
    
    /// 获取故事音频URL数组
    /// - Returns: 音频URL数组
    func getStoryAudioURLs() -> [URL] {
        if let audioData = self.audioRecordings, let urls = try? NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(audioData) as? [URL] {
            return urls
        }
        return []
    }
} 