import XCTest
import Swift<PERSON>
@testable import PinkLog

class AddProductViewStateTests: XCTestCase {
    
    var testImage: UIImage!
    var testLiftedImage: UIImage!
    var testStickerImage: UIImage!
    
    override func setUp() {
        super.setUp()
        
        // 创建测试图片
        let size = CGSize(width: 1000, height: 1000)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        testImage = renderer.image { context in
            UIColor.red.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
        
        testLiftedImage = renderer.image { context in
            UIColor.blue.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
        
        testStickerImage = renderer.image { context in
            UIColor.green.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
    }
    
    override func tearDown() {
        testImage = nil
        testLiftedImage = nil
        testStickerImage = nil
        super.tearDown()
    }
    
    /// 测试图片状态管理优化
    func testImageStateManagement() {
        let mockAddProductView = MockAddProductView()
        
        // 模拟拍照后设置图片
        mockAddProductView.selectedImage = testImage
        mockAddProductView.liftedImage = testLiftedImage
        mockAddProductView.stickerImage = testStickerImage
        
        XCTAssertNotNil(mockAddProductView.selectedImage)
        XCTAssertNotNil(mockAddProductView.liftedImage)
        XCTAssertNotNil(mockAddProductView.stickerImage)
        XCTAssertNil(mockAddProductView.finalImageData)
        
        // 模拟SubjectLiftView回调，选择贴纸图片
        let compressedData = ImageManager.shared.compressToTargetSize(
            testStickerImage,
            maxSize: 512,
            quality: 0.3,
            targetBytes: 100 * 1024
        )
        
        XCTAssertNotNil(compressedData)
        
        // 模拟回调处理
        mockAddProductView.handleImageSelection(compressedData: compressedData!, displayImage: testStickerImage)
        
        // 验证状态
        XCTAssertNotNil(mockAddProductView.finalImageData)
        XCTAssertNotNil(mockAddProductView.selectedImage)
        XCTAssertEqual(mockAddProductView.selectedImage, testStickerImage)
        
        if let data = mockAddProductView.finalImageData {
            XCTAssertLessThanOrEqual(data.count, 100 * 1024, "最终图片数据应小于100KB")
        }
    }
    
    /// 测试更换图片时的状态清理
    func testImageReplacementCleanup() {
        let mockAddProductView = MockAddProductView()
        
        // 设置初始图片和数据
        mockAddProductView.selectedImage = testImage
        mockAddProductView.liftedImage = testLiftedImage
        mockAddProductView.stickerImage = testStickerImage
        mockAddProductView.finalImageData = Data([1, 2, 3, 4, 5]) // 模拟数据
        
        // 验证初始状态
        XCTAssertNotNil(mockAddProductView.selectedImage)
        XCTAssertNotNil(mockAddProductView.liftedImage)
        XCTAssertNotNil(mockAddProductView.stickerImage)
        XCTAssertNotNil(mockAddProductView.finalImageData)
        
        // 模拟更换图片操作
        mockAddProductView.clearAllImageData()
        
        // 验证清理后的状态
        XCTAssertNil(mockAddProductView.selectedImage)
        XCTAssertNil(mockAddProductView.liftedImage)
        XCTAssertNil(mockAddProductView.stickerImage)
        XCTAssertNil(mockAddProductView.finalImageData)
    }
    
    /// 测试保存时的图片数据使用
    func testSaveWithOptimizedImageData() {
        let mockAddProductView = MockAddProductView()
        
        // 设置压缩后的图片数据
        let compressedData = ImageManager.shared.compressToTargetSize(
            testImage,
            maxSize: 512,
            quality: 0.3,
            targetBytes: 100 * 1024
        )
        
        mockAddProductView.finalImageData = compressedData
        mockAddProductView.selectedImage = testImage
        
        // 模拟保存操作
        let imageDataForSave = mockAddProductView.getImageDataForSave()
        
        XCTAssertNotNil(imageDataForSave)
        XCTAssertEqual(imageDataForSave, compressedData, "应该优先使用压缩后的数据")
        
        if let data = imageDataForSave {
            XCTAssertLessThanOrEqual(data.count, 100 * 1024, "保存的图片数据应小于100KB")
        }
    }
    
    /// 测试降级处理（没有压缩数据时的处理）
    func testFallbackImageProcessing() {
        let mockAddProductView = MockAddProductView()
        
        // 只设置selectedImage，没有finalImageData
        mockAddProductView.selectedImage = testImage
        mockAddProductView.finalImageData = nil
        
        // 模拟保存操作
        let imageDataForSave = mockAddProductView.getImageDataForSave()
        
        XCTAssertNotNil(imageDataForSave, "应该有降级处理的图片数据")
        
        // 验证降级处理的数据也是压缩过的
        if let data = imageDataForSave {
            // 降级处理应该也会压缩图片
            XCTAssertLessThan(data.count, testImage.jpegData(compressionQuality: 1.0)?.count ?? 0, "降级处理也应该压缩图片")
        }
    }
    
    /// 测试内存使用监控
    func testMemoryMonitoring() {
        let mockAddProductView = MockAddProductView()
        
        // 获取初始内存使用
        let initialMemory = ImageManager.shared.getMemoryUsage()
        XCTAssertFalse(initialMemory.isEmpty, "应该能获取内存使用信息")
        
        // 设置大图片
        mockAddProductView.selectedImage = testImage
        mockAddProductView.liftedImage = testLiftedImage
        mockAddProductView.stickerImage = testStickerImage
        
        // 模拟内存监控
        mockAddProductView.logMemoryUsage("测试内存监控")
        
        // 清理图片
        mockAddProductView.clearAllImageData()
        
        // 再次监控
        mockAddProductView.logMemoryUsage("清理后内存监控")
        
        // 这个测试主要验证监控功能不会崩溃
        XCTAssertTrue(true, "内存监控功能正常工作")
    }
}

// MARK: - Mock Classes

/// 模拟AddProductView的图片状态管理逻辑
class MockAddProductView {
    var selectedImage: UIImage?
    var liftedImage: UIImage?
    var stickerImage: UIImage?
    var finalImageData: Data?
    
    /// 模拟SubjectLiftView回调处理
    func handleImageSelection(compressedData: Data, displayImage: UIImage) {
        finalImageData = compressedData
        selectedImage = displayImage
        logMemoryUsage("图片选择完成")
    }
    
    /// 模拟更换图片时的清理操作
    func clearAllImageData() {
        finalImageData = nil
        selectedImage = nil
        liftedImage = nil
        stickerImage = nil
    }
    
    /// 模拟保存时获取图片数据的逻辑
    func getImageDataForSave() -> Data? {
        // 优先使用压缩后的数据
        if let imageData = finalImageData {
            return imageData
        } else if let image = selectedImage {
            // 降级处理：使用原有逻辑
            return ImageManager.shared.processTransparentImage(image) ?? image.jpegData(compressionQuality: 0.8)
        }
        return nil
    }
    
    /// 模拟内存监控
    func logMemoryUsage(_ context: String) {
        #if DEBUG
        let memoryInfo = ImageManager.shared.getMemoryUsage()
        print("[\(context)] \(memoryInfo)")
        #endif
    }
}
