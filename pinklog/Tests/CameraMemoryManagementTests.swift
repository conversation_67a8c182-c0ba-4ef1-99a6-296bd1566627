import XCTest
@testable import PinkLog
import UIKit

class CameraMemoryManagementTests: XCTestCase {
    
    var testImage: UIImage!
    var subjectLiftManager: SubjectLiftManager!
    
    override func setUp() {
        super.setUp()
        subjectLiftManager = SubjectLiftManager.shared
        
        // 创建测试图片
        let size = CGSize(width: 1000, height: 1000)
        let renderer = UIGraphicsImageRenderer(size: size)
        testImage = renderer.image { context in
            UIColor.blue.setFill()
            context.fill(CGRect(origin: .zero, size: size))
            
            // 添加一些内容让抠图算法有东西可处理
            UIColor.white.setFill()
            context.fill(CGRect(x: 300, y: 300, width: 400, height: 400))
        }
    }
    
    override func tearDown() {
        testImage = nil
        super.tearDown()
    }
    
    /// 测试SubjectLiftManager的内存管理
    func testSubjectLiftManagerMemoryManagement() {
        let initialMemory = getMemoryUsage()
        let expectation = XCTestExpectation(description: "抠图处理完成")
        
        subjectLiftManager.autoProcessImage(from: testImage) { result in
            let finalMemory = self.getMemoryUsage()
            let memoryIncrease = finalMemory - initialMemory
            
            switch result {
            case .success(let (liftedImage, stickerImage)):
                XCTAssertNotNil(liftedImage, "抠图应该成功")
                XCTAssertNotNil(stickerImage, "贴纸应该成功")
                
                // 验证内存增长在合理范围内
                XCTAssertLessThan(memoryIncrease, 100.0, "内存增长应控制在100MB以内")
                
            case .failure(let error):
                // 即使失败，内存增长也应该在合理范围内
                XCTAssertLessThan(memoryIncrease, 50.0, "即使处理失败，内存增长也应控制在50MB以内")
                print("抠图失败: \(error.localizedDescription)")
            }
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 30.0)
    }
    
    /// 测试多次处理的内存累积
    func testMultipleProcessingMemoryAccumulation() {
        let initialMemory = getMemoryUsage()
        let expectation = XCTestExpectation(description: "多次处理完成")
        expectation.expectedFulfillmentCount = 3
        
        var memoryReadings: [Double] = []
        
        // 进行3次处理，验证内存不会过度累积
        for i in 0..<3 {
            subjectLiftManager.autoProcessImage(from: testImage) { result in
                let currentMemory = self.getMemoryUsage()
                memoryReadings.append(currentMemory)
                
                expectation.fulfill()
                
                // 如果是最后一次，验证内存累积
                if memoryReadings.count == 3 {
                    let finalMemory = memoryReadings.last!
                    let totalIncrease = finalMemory - initialMemory
                    
                    // 验证总内存增长不会线性累积
                    XCTAssertLessThan(totalIncrease, 200.0, "多次处理后总内存增长应控制在200MB以内")
                    
                    // 验证内存增长趋势不是线性的（说明有内存清理）
                    if memoryReadings.count >= 2 {
                        let firstIncrease = memoryReadings[0] - initialMemory
                        let secondIncrease = memoryReadings[1] - initialMemory
                        let thirdIncrease = memoryReadings[2] - initialMemory
                        
                        // 第三次的增长不应该是第一次的3倍（说明有内存清理）
                        XCTAssertLessThan(thirdIncrease, firstIncrease * 2.5, "内存增长不应该线性累积")
                    }
                }
            }
            
            // 在处理间添加短暂延迟，模拟真实使用场景
            Thread.sleep(forTimeInterval: 0.5)
        }
        
        wait(for: [expectation], timeout: 60.0)
    }
    
    /// 测试autoreleasepool的效果
    func testAutoreleasepoolEffectiveness() {
        let initialMemory = getMemoryUsage()
        
        // 模拟大量图片处理操作
        for i in 0..<5 {
            autoreleasepool {
                let tempImage = createLargeTestImage()
                
                // 模拟图片处理操作
                let compressedData = ImageManager.shared.compressToTargetSize(
                    tempImage,
                    maxSize: 512,
                    quality: 0.3,
                    targetBytes: 100 * 1024
                )
                
                XCTAssertNotNil(compressedData, "压缩应该成功")
            }
        }
        
        // 强制内存回收
        DispatchQueue.main.async {
            // 触发内存清理
        }
        
        // 等待内存回收
        Thread.sleep(forTimeInterval: 1.0)
        
        let finalMemory = getMemoryUsage()
        let memoryIncrease = finalMemory - initialMemory
        
        // 验证autoreleasepool有效控制了内存增长
        XCTAssertLessThan(memoryIncrease, 50.0, "autoreleasepool应该有效控制内存增长")
    }
    
    /// 测试内存压力下的处理能力
    func testMemoryPressureHandling() {
        let initialMemory = getMemoryUsage()
        
        // 如果当前内存使用已经很高，跳过这个测试
        if initialMemory > 500.0 {
            XCTSkip("当前内存使用过高，跳过内存压力测试")
        }
        
        let expectation = XCTestExpectation(description: "内存压力测试完成")
        
        // 创建一个较大的图片进行处理
        let largeImage = createLargeTestImage()
        
        subjectLiftManager.autoProcessImage(from: largeImage) { result in
            let finalMemory = self.getMemoryUsage()
            let memoryIncrease = finalMemory - initialMemory
            
            // 即使处理大图片，内存增长也应该在合理范围内
            XCTAssertLessThan(memoryIncrease, 150.0, "处理大图片时内存增长应控制在150MB以内")
            
            switch result {
            case .success(_):
                print("大图片处理成功")
            case .failure(let error):
                print("大图片处理失败: \(error.localizedDescription)")
            }
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 45.0)
    }
    
    /// 测试内存监控功能
    func testMemoryMonitoring() {
        let memoryInfo = ImageManager.shared.getMemoryUsage()
        
        XCTAssertFalse(memoryInfo.isEmpty, "内存监控应该返回有效信息")
        XCTAssertTrue(memoryInfo.contains("内存使用"), "内存信息应包含'内存使用'文字")
        
        // 验证内存监控不会崩溃
        for _ in 0..<10 {
            let info = ImageManager.shared.getMemoryUsage()
            XCTAssertFalse(info.isEmpty, "多次调用内存监控应该稳定")
        }
    }
    
    // MARK: - Helper Methods
    
    /// 获取当前内存使用量（MB）
    private func getMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / 1024.0 / 1024.0
        } else {
            return 0.0
        }
    }
    
    /// 创建大尺寸测试图片
    private func createLargeTestImage() -> UIImage {
        let size = CGSize(width: 2000, height: 2000)
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            // 创建渐变背景
            let colors = [UIColor.red, UIColor.blue, UIColor.green, UIColor.yellow]
            for (index, color) in colors.enumerated() {
                color.setFill()
                let rect = CGRect(
                    x: CGFloat(index % 2) * size.width / 2,
                    y: CGFloat(index / 2) * size.height / 2,
                    width: size.width / 2,
                    height: size.height / 2
                )
                context.fill(rect)
            }
        }
    }
}
