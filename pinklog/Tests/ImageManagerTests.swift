import XCTest
@testable import PinkLog
import UIKit

class ImageManagerTests: XCTestCase {
    
    var imageManager: ImageManager!
    
    override func setUp() {
        super.setUp()
        imageManager = ImageManager.shared
    }
    
    override func tearDown() {
        imageManager.clearCache()
        super.tearDown()
    }
    
    /// 测试压缩到目标大小功能
    func testCompressToTargetSize() {
        // 创建一个大图片用于测试
        let size = CGSize(width: 2000, height: 2000)
        let renderer = UIGraphicsImageRenderer(size: size)
        let testImage = renderer.image { context in
            UIColor.red.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
        
        // 测试压缩到100KB以下
        let compressedData = imageManager.compressToTargetSize(testImage, maxSize: 512, quality: 0.3, targetBytes: 100 * 1024)
        
        XCTAssertNotNil(compressedData, "压缩后的数据不应为nil")
        
        if let data = compressedData {
            XCTAssertLessThanOrEqual(data.count, 100 * 1024, "压缩后的数据应小于100KB")
            
            // 验证压缩后的图片仍然可用
            let compressedImage = UIImage(data: data)
            XCTAssertNotNil(compressedImage, "压缩后的图片应该可以正常加载")
            
            if let image = compressedImage {
                XCTAssertLessThanOrEqual(max(image.size.width, image.size.height), 512, "压缩后的图片尺寸应不超过512px")
            }
        }
    }
    
    /// 测试透明图片处理
    func testProcessTransparentImage() {
        // 创建一个带透明背景的图片
        let size = CGSize(width: 1000, height: 1000)
        let renderer = UIGraphicsImageRenderer(size: size)
        let transparentImage = renderer.image { context in
            UIColor.clear.setFill()
            context.fill(CGRect(origin: .zero, size: size))
            
            UIColor.blue.setFill()
            context.fill(CGRect(x: 100, y: 100, width: 800, height: 800))
        }
        
        let processedData = imageManager.processTransparentImage(transparentImage)
        
        XCTAssertNotNil(processedData, "处理后的数据不应为nil")
        
        if let data = processedData {
            XCTAssertLessThanOrEqual(data.count, 100 * 1024, "处理后的数据应小于100KB")
            
            let processedImage = UIImage(data: data)
            XCTAssertNotNil(processedImage, "处理后的图片应该可以正常加载")
        }
    }
    
    /// 测试内存清理功能
    func testClearImageReferences() {
        var images: [UIImage?] = []
        
        // 创建一些测试图片
        for i in 0..<5 {
            let size = CGSize(width: 100, height: 100)
            let renderer = UIGraphicsImageRenderer(size: size)
            let image = renderer.image { context in
                UIColor(red: CGFloat(i) / 5.0, green: 0.5, blue: 0.5, alpha: 1.0).setFill()
                context.fill(CGRect(origin: .zero, size: size))
            }
            images.append(image)
        }
        
        // 验证图片已创建
        XCTAssertEqual(images.count, 5, "应该有5个图片")
        XCTAssertTrue(images.allSatisfy { $0 != nil }, "所有图片都应该不为nil")
        
        // 清理图片引用
        imageManager.clearImageReferences(&images)
        
        // 验证清理结果
        XCTAssertEqual(images.count, 0, "清理后数组应为空")
    }
    
    /// 测试单个图片引用清理
    func testClearImageReference() {
        let size = CGSize(width: 100, height: 100)
        let renderer = UIGraphicsImageRenderer(size: size)
        var testImage: UIImage? = renderer.image { context in
            UIColor.red.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
        
        XCTAssertNotNil(testImage, "测试图片应该不为nil")
        
        imageManager.clearImageReference(&testImage)
        
        XCTAssertNil(testImage, "清理后图片应该为nil")
    }
    
    /// 测试内存使用监控
    func testGetMemoryUsage() {
        let memoryInfo = imageManager.getMemoryUsage()
        
        XCTAssertFalse(memoryInfo.isEmpty, "内存信息不应为空")
        XCTAssertTrue(memoryInfo.contains("内存使用"), "内存信息应包含'内存使用'文字")
    }
    
    /// 测试压缩质量边界情况
    func testCompressToTargetSizeEdgeCases() {
        // 测试非常小的目标大小
        let size = CGSize(width: 1000, height: 1000)
        let renderer = UIGraphicsImageRenderer(size: size)
        let testImage = renderer.image { context in
            UIColor.blue.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
        
        // 测试极小的目标大小（1KB）
        let verySmallData = imageManager.compressToTargetSize(testImage, maxSize: 512, quality: 0.3, targetBytes: 1024)
        XCTAssertNotNil(verySmallData, "即使目标很小，也应该返回数据")
        
        // 测试已经很小的图片
        let smallSize = CGSize(width: 100, height: 100)
        let smallRenderer = UIGraphicsImageRenderer(size: smallSize)
        let smallImage = smallRenderer.image { context in
            UIColor.green.setFill()
            context.fill(CGRect(origin: .zero, size: smallSize))
        }
        
        let smallImageData = imageManager.compressToTargetSize(smallImage, maxSize: 512, quality: 0.3, targetBytes: 100 * 1024)
        XCTAssertNotNil(smallImageData, "小图片压缩应该成功")
        
        if let data = smallImageData {
            XCTAssertLessThanOrEqual(data.count, 100 * 1024, "小图片压缩后应小于目标大小")
        }
    }
}
