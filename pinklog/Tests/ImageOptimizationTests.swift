import XCTest
@testable import PinkLog
import UIKit

class ImageOptimizationTests: XCTestCase {
    
    var memoryMonitor: MemoryMonitor!
    var imageManager: ImageManager!
    var testImages: [UIImage] = []
    
    override func setUp() {
        super.setUp()
        memoryMonitor = MemoryMonitor.shared
        imageManager = ImageManager.shared
        
        // 清除之前的读数
        memoryMonitor.clearReadings()
        
        // 创建多个测试图片
        createTestImages()
        
        // 记录初始内存使用
        memoryMonitor.recordMemoryUsage("测试开始")
    }
    
    override func tearDown() {
        // 清理测试图片
        testImages.removeAll()
        
        // 清理缓存
        imageManager.clearCache()
        
        // 记录结束内存使用
        memoryMonitor.recordMemoryUsage("测试结束")
        
        super.tearDown()
    }
    
    /// 创建测试图片
    private func createTestImages() {
        let sizes = [
            CGSize(width: 1000, height: 1000),
            CGSize(width: 1500, height: 1500),
            CGSize(width: 2000, height: 2000)
        ]
        
        for (index, size) in sizes.enumerated() {
            let renderer = UIGraphicsImageRenderer(size: size)
            let image = renderer.image { context in
                let colors = [UIColor.red, UIColor.green, UIColor.blue]
                colors[index % colors.count].setFill()
                context.fill(CGRect(origin: .zero, size: size))
            }
            testImages.append(image)
        }
    }
    
    /// 测试整体内存优化效果
    func testOverallMemoryOptimization() {
        let initialStats = memoryMonitor.getMemoryStatistics()
        
        // 模拟完整的添加物品流程
        memoryMonitor.monitorMemoryUsage("完整添加物品流程") {
            for (index, testImage) in testImages.enumerated() {
                // 1. 压缩图片
                let compressedData = imageManager.compressToTargetSize(
                    testImage,
                    maxSize: 512,
                    quality: 0.3,
                    targetBytes: 100 * 1024
                )
                
                XCTAssertNotNil(compressedData, "图片\(index)压缩应该成功")
                
                if let data = compressedData {
                    XCTAssertLessThanOrEqual(data.count, 100 * 1024, "压缩后图片\(index)应小于100KB")
                }
                
                // 2. 清理原图引用
                // 模拟清理操作
                autoreleasepool {
                    // 临时处理
                }
            }
        }
        
        let finalStats = memoryMonitor.getMemoryStatistics()
        
        // 验证内存使用在合理范围内
        XCTAssertLessThan(finalStats.currentUsage, 500.0, "优化后内存使用应小于500MB")
        
        // 验证没有严重的内存警告
        XCTAssertEqual(finalStats.criticalCount, 0, "不应该有严重内存警告")
        
        print("内存优化测试结果:")
        print(finalStats.description)
    }
    
    /// 测试图片质量验证
    func testImageQualityAfterOptimization() {
        for (index, testImage) in testImages.enumerated() {
            let compressedData = imageManager.compressToTargetSize(
                testImage,
                maxSize: 512,
                quality: 0.3,
                targetBytes: 100 * 1024
            )
            
            XCTAssertNotNil(compressedData, "图片\(index)压缩应该成功")
            
            if let data = compressedData {
                let compressedImage = UIImage(data: data)
                XCTAssertNotNil(compressedImage, "压缩后的图片\(index)应该可以正常加载")
                
                if let image = compressedImage {
                    // 验证图片尺寸
                    let maxDimension = max(image.size.width, image.size.height)
                    XCTAssertLessThanOrEqual(maxDimension, 512, "压缩后图片\(index)尺寸应不超过512px")
                    
                    // 验证图片不是空白的
                    XCTAssertGreaterThan(image.size.width, 0, "压缩后图片\(index)宽度应大于0")
                    XCTAssertGreaterThan(image.size.height, 0, "压缩后图片\(index)高度应大于0")
                }
            }
        }
    }
    
    /// 测试功能完整性
    func testFunctionalityCompleteness() {
        // 测试ImageManager的所有关键功能
        let testImage = testImages.first!
        
        // 1. 基础压缩功能
        let compressedImage = imageManager.compressImage(testImage, maxSize: 512)
        XCTAssertNotNil(compressedImage, "基础压缩功能应该正常")
        
        // 2. 目标大小压缩功能
        let targetCompressedData = imageManager.compressToTargetSize(testImage)
        XCTAssertNotNil(targetCompressedData, "目标大小压缩功能应该正常")
        
        // 3. 透明图片处理功能
        let transparentProcessedData = imageManager.processTransparentImage(testImage)
        XCTAssertNotNil(transparentProcessedData, "透明图片处理功能应该正常")
        
        // 4. 渐进式JPEG功能
        let progressiveData = imageManager.compressToProgressiveJPEG(testImage)
        XCTAssertNotNil(progressiveData, "渐进式JPEG功能应该正常")
        
        // 5. 缓存功能
        imageManager.cacheImage(compressedImage, key: "test_cache")
        let cachedImage = imageManager.getImageFromCache(key: "test_cache")
        XCTAssertNotNil(cachedImage, "缓存功能应该正常")
        
        // 6. 内存清理功能
        var testImageRef: UIImage? = testImage
        imageManager.clearImageReference(&testImageRef)
        XCTAssertNil(testImageRef, "内存清理功能应该正常")
    }
    
    /// 测试用户体验保持
    func testUserExperiencePreservation() {
        let startTime = Date()
        
        // 测试处理速度
        for testImage in testImages {
            let processingStart = Date()
            
            let compressedData = imageManager.compressToTargetSize(
                testImage,
                maxSize: 512,
                quality: 0.3,
                targetBytes: 100 * 1024
            )
            
            let processingTime = Date().timeIntervalSince(processingStart)
            
            XCTAssertNotNil(compressedData, "图片处理应该成功")
            XCTAssertLessThan(processingTime, 5.0, "单张图片处理时间应小于5秒")
        }
        
        let totalTime = Date().timeIntervalSince(startTime)
        XCTAssertLessThan(totalTime, 15.0, "总处理时间应小于15秒")
    }
    
    /// 测试内存监控功能
    func testMemoryMonitoringFunctionality() {
        // 测试内存监控基础功能
        let initialUsage = memoryMonitor.getCurrentMemoryUsage()
        XCTAssertGreaterThan(initialUsage, 0, "应该能获取内存使用量")
        
        // 测试记录功能
        memoryMonitor.recordMemoryUsage("测试记录")
        let stats = memoryMonitor.getMemoryStatistics()
        XCTAssertGreaterThan(stats.totalReadings, 0, "应该有内存记录")
        
        // 测试阈值设置
        memoryMonitor.setMemoryThresholds(warning: 400.0, critical: 600.0)
        
        // 测试监控代码块功能
        let result = memoryMonitor.monitorMemoryUsage("测试代码块") {
            return "测试结果"
        }
        XCTAssertEqual(result, "测试结果", "监控代码块应该正常返回结果")
        
        // 测试导出功能
        let readings = memoryMonitor.exportReadings()
        XCTAssertFalse(readings.isEmpty, "应该有内存读数可以导出")
    }
    
    /// 测试内存压力场景
    func testMemoryPressureScenarios() {
        let initialMemory = memoryMonitor.getCurrentMemoryUsage()
        
        // 如果初始内存已经很高，跳过这个测试
        if initialMemory > 400.0 {
            XCTSkip("初始内存使用过高，跳过内存压力测试")
        }
        
        // 模拟高内存压力场景
        var largeImages: [UIImage] = []
        
        memoryMonitor.monitorMemoryUsage("内存压力测试") {
            // 创建多个大图片
            for i in 0..<5 {
                let size = CGSize(width: 2000, height: 2000)
                let renderer = UIGraphicsImageRenderer(size: size)
                let largeImage = renderer.image { context in
                    UIColor(red: CGFloat(i) / 5.0, green: 0.5, blue: 0.5, alpha: 1.0).setFill()
                    context.fill(CGRect(origin: .zero, size: size))
                }
                largeImages.append(largeImage)
                
                // 立即压缩处理
                let compressedData = imageManager.compressToTargetSize(
                    largeImage,
                    maxSize: 512,
                    quality: 0.3,
                    targetBytes: 100 * 1024
                )
                
                XCTAssertNotNil(compressedData, "即使在内存压力下也应该能处理图片")
            }
        }
        
        // 清理大图片
        largeImages.removeAll()
        
        // 强制内存清理
        autoreleasepool {
            // 触发清理
        }
        
        let finalMemory = memoryMonitor.getCurrentMemoryUsage()
        let memoryIncrease = finalMemory - initialMemory
        
        // 验证内存增长在合理范围内
        XCTAssertLessThan(memoryIncrease, 200.0, "内存压力测试后增长应控制在200MB以内")
    }
    
    /// 测试优化前后对比
    func testOptimizationComparison() {
        // 模拟优化前的行为（同时保存多种图片）
        memoryMonitor.recordMemoryUsage("模拟优化前 - 开始")
        
        var originalImages: [UIImage] = []
        var liftedImages: [UIImage] = []
        var stickerImages: [UIImage] = []
        
        // 模拟同时保存三种图片
        for testImage in testImages {
            originalImages.append(testImage)
            liftedImages.append(testImage) // 模拟抠图
            stickerImages.append(testImage) // 模拟贴纸
        }
        
        memoryMonitor.recordMemoryUsage("模拟优化前 - 三种图片都保存")
        
        // 清理模拟的优化前状态
        originalImages.removeAll()
        liftedImages.removeAll()
        stickerImages.removeAll()
        
        memoryMonitor.recordMemoryUsage("模拟优化后 - 开始")
        
        // 模拟优化后的行为（只保存压缩后的最终图片）
        var finalCompressedData: [Data] = []
        
        for testImage in testImages {
            if let compressedData = imageManager.compressToTargetSize(
                testImage,
                maxSize: 512,
                quality: 0.3,
                targetBytes: 100 * 1024
            ) {
                finalCompressedData.append(compressedData)
            }
        }
        
        memoryMonitor.recordMemoryUsage("模拟优化后 - 只保存压缩数据")
        
        // 验证优化后的数据大小
        let totalCompressedSize = finalCompressedData.reduce(0) { $0 + $1.count }
        let averageCompressedSize = totalCompressedSize / finalCompressedData.count
        
        XCTAssertLessThan(averageCompressedSize, 100 * 1024, "平均压缩后大小应小于100KB")
        
        // 输出对比结果
        let stats = memoryMonitor.getMemoryStatistics()
        print("优化对比测试结果:")
        print(stats.description)
    }

    /// 测试完整的添加物品流程内存优化
    func testCompleteAddProductFlowOptimization() {
        let expectation = XCTestExpectation(description: "完整流程测试完成")

        memoryMonitor.recordMemoryUsage("完整流程测试 - 开始")

        // 模拟拍照
        let testImage = testImages.first!
        memoryMonitor.recordMemoryUsage("模拟拍照完成")

        // 模拟抠图和贴纸处理
        SubjectLiftManager.shared.autoProcessImage(from: testImage) { result in
            self.memoryMonitor.recordMemoryUsage("抠图和贴纸处理完成")

            switch result {
            case .success(let (liftedImage, stickerImage)):
                // 模拟用户选择贴纸风格
                let finalCompressedData = self.imageManager.compressToTargetSize(
                    stickerImage,
                    maxSize: 512,
                    quality: 0.3,
                    targetBytes: 100 * 1024
                )

                XCTAssertNotNil(finalCompressedData, "最终图片压缩应该成功")

                if let data = finalCompressedData {
                    XCTAssertLessThanOrEqual(data.count, 100 * 1024, "最终图片应小于100KB")
                }

                self.memoryMonitor.recordMemoryUsage("最终图片压缩完成")

                // 模拟清理其他图片引用
                // 在实际应用中，这里会清理originalImage和liftedImage
                autoreleasepool {
                    // 模拟清理操作
                }

                self.memoryMonitor.recordMemoryUsage("清理其他图片引用完成")

            case .failure(let error):
                print("抠图处理失败: \(error.localizedDescription)")
                // 即使失败也要记录内存使用
                self.memoryMonitor.recordMemoryUsage("抠图处理失败")
            }

            expectation.fulfill()
        }

        wait(for: [expectation], timeout: 30.0)

        memoryMonitor.recordMemoryUsage("完整流程测试 - 结束")

        // 验证整个流程的内存使用
        let finalStats = memoryMonitor.getMemoryStatistics()
        XCTAssertLessThan(finalStats.currentUsage, 500.0, "完整流程后内存使用应小于500MB")

        print("完整添加物品流程内存优化测试结果:")
        print(finalStats.description)
    }
}
