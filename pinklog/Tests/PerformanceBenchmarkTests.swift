import XCTest
@testable import PinkLog
import UIKit

class PerformanceBenchmarkTests: XCTestCase {
    
    var memoryMonitor: MemoryMonitor!
    var imageManager: ImageManager!
    
    override func setUp() {
        super.setUp()
        memoryMonitor = MemoryMonitor.shared
        imageManager = ImageManager.shared
        
        // 设置内存阈值
        memoryMonitor.setMemoryThresholds(warning: 400.0, critical: 600.0)
        memoryMonitor.clearReadings()
        
        memoryMonitor.recordMemoryUsage("性能基准测试开始")
    }
    
    override func tearDown() {
        imageManager.clearCache()
        memoryMonitor.recordMemoryUsage("性能基准测试结束")
        
        // 输出最终统计
        let finalStats = memoryMonitor.getMemoryStatistics()
        print("性能基准测试最终统计:")
        print(finalStats.description)
        
        super.tearDown()
    }
    
    /// 基准测试：优化后的图片处理性能
    func testOptimizedImageProcessingPerformance() {
        let testImages = createBenchmarkImages()
        
        measure {
            memoryMonitor.monitorMemoryUsage("优化后图片处理性能测试") {
                for (index, image) in testImages.enumerated() {
                    // 使用优化后的压缩方法
                    let compressedData = imageManager.compressToTargetSize(
                        image,
                        maxSize: 512,
                        quality: 0.3,
                        targetBytes: 100 * 1024
                    )
                    
                    XCTAssertNotNil(compressedData, "图片\(index)压缩应该成功")
                    
                    if let data = compressedData {
                        XCTAssertLessThanOrEqual(data.count, 100 * 1024, "压缩后图片\(index)应小于100KB")
                    }
                    
                    // 立即清理引用
                    autoreleasepool {
                        // 模拟清理
                    }
                }
            }
        }
        
        // 验证内存使用在合理范围内
        let stats = memoryMonitor.getMemoryStatistics()
        XCTAssertLessThan(stats.currentUsage, 500.0, "优化后内存使用应小于500MB")
        XCTAssertEqual(stats.criticalCount, 0, "不应该有严重内存警告")
    }
    
    /// 基准测试：渐进式JPEG优化性能
    func testProgressiveJPEGOptimizationPerformance() {
        let testImage = createLargeTestImage()
        let testData = testImage.jpegData(compressionQuality: 0.8)!
        
        measure {
            memoryMonitor.monitorMemoryUsage("渐进式JPEG优化性能测试") {
                let expectation = XCTestExpectation(description: "渐进式JPEG加载完成")
                
                var progressCount = 0
                
                imageManager.loadProgressiveJPEGAsync(
                    data: testData,
                    key: "benchmark_progressive"
                ) { image, quality in
                    progressCount += 1
                } completion: { finalImage in
                    XCTAssertNotNil(finalImage, "渐进式JPEG加载应该成功")
                    XCTAssertLessThanOrEqual(progressCount, 5, "渐进式JPEG质量级别应不超过5个")
                    expectation.fulfill()
                }
                
                wait(for: [expectation], timeout: 5.0)
            }
        }
    }
    
    /// 基准测试：内存清理效果
    func testMemoryCleanupEffectiveness() {
        let initialMemory = memoryMonitor.getCurrentMemoryUsage()
        
        // 创建大量图片对象
        var images: [UIImage] = []
        
        memoryMonitor.recordMemoryUsage("创建大量图片前")
        
        for i in 0..<10 {
            let image = createTestImage(size: CGSize(width: 1000, height: 1000), color: UIColor.red)
            images.append(image)
        }
        
        memoryMonitor.recordMemoryUsage("创建大量图片后")
        
        // 测试清理效果
        measure {
            memoryMonitor.monitorMemoryUsage("内存清理效果测试") {
                // 清理图片引用
                imageManager.clearImageReferences(&images)
                
                // 强制内存回收
                autoreleasepool {
                    // 触发清理
                }
            }
        }
        
        memoryMonitor.recordMemoryUsage("清理完成后")
        
        let finalMemory = memoryMonitor.getCurrentMemoryUsage()
        let memoryIncrease = finalMemory - initialMemory
        
        // 验证内存清理效果
        XCTAssertLessThan(memoryIncrease, 50.0, "清理后内存增长应小于50MB")
        XCTAssertEqual(images.count, 0, "图片数组应该被清空")
    }
    
    /// 基准测试：完整添加物品流程性能
    func testCompleteAddProductFlowPerformance() {
        let testImage = createLargeTestImage()
        
        measure {
            memoryMonitor.monitorMemoryUsage("完整添加物品流程性能测试") {
                let expectation = XCTestExpectation(description: "完整流程完成")
                
                // 1. 模拟拍照
                memoryMonitor.recordMemoryUsage("拍照完成")
                
                // 2. 抠图和贴纸处理
                SubjectLiftManager.shared.autoProcessImage(from: testImage) { result in
                    self.memoryMonitor.recordMemoryUsage("抠图处理完成")
                    
                    switch result {
                    case .success(let (liftedImage, stickerImage)):
                        // 3. 用户选择图片风格并压缩
                        let finalData = self.imageManager.compressToTargetSize(
                            stickerImage,
                            maxSize: 512,
                            quality: 0.3,
                            targetBytes: 100 * 1024
                        )
                        
                        XCTAssertNotNil(finalData, "最终图片压缩应该成功")
                        self.memoryMonitor.recordMemoryUsage("最终压缩完成")
                        
                        // 4. 清理其他图片引用
                        autoreleasepool {
                            // 模拟清理originalImage和liftedImage
                        }
                        
                        self.memoryMonitor.recordMemoryUsage("清理完成")
                        
                    case .failure(_):
                        self.memoryMonitor.recordMemoryUsage("抠图失败")
                    }
                    
                    expectation.fulfill()
                }
                
                wait(for: [expectation], timeout: 30.0)
            }
        }
        
        // 验证整个流程的性能
        let stats = memoryMonitor.getMemoryStatistics()
        XCTAssertLessThan(stats.currentUsage, 500.0, "完整流程后内存应小于500MB")
    }
    
    /// 基准测试：并发处理性能
    func testConcurrentProcessingPerformance() {
        let testImages = createBenchmarkImages()
        
        measure {
            memoryMonitor.monitorMemoryUsage("并发处理性能测试") {
                let expectation = XCTestExpectation(description: "并发处理完成")
                expectation.expectedFulfillmentCount = testImages.count
                
                let queue = DispatchQueue(label: "concurrent.test", attributes: .concurrent)
                
                for (index, image) in testImages.enumerated() {
                    queue.async {
                        autoreleasepool {
                            let compressedData = self.imageManager.compressToTargetSize(
                                image,
                                maxSize: 512,
                                quality: 0.3,
                                targetBytes: 100 * 1024
                            )
                            
                            XCTAssertNotNil(compressedData, "并发处理图片\(index)应该成功")
                            
                            DispatchQueue.main.async {
                                expectation.fulfill()
                            }
                        }
                    }
                }
                
                wait(for: [expectation], timeout: 15.0)
            }
        }
        
        memoryMonitor.recordMemoryUsage("并发处理完成")
    }
    
    // MARK: - Helper Methods
    
    private func createBenchmarkImages() -> [UIImage] {
        let sizes = [
            CGSize(width: 800, height: 800),
            CGSize(width: 1200, height: 1200),
            CGSize(width: 1600, height: 1600),
            CGSize(width: 2000, height: 2000)
        ]
        
        return sizes.enumerated().map { index, size in
            createTestImage(size: size, color: UIColor(hue: CGFloat(index) / 4.0, saturation: 1.0, brightness: 1.0, alpha: 1.0))
        }
    }
    
    private func createLargeTestImage() -> UIImage {
        return createTestImage(size: CGSize(width: 2000, height: 2000), color: UIColor.blue)
    }
    
    private func createTestImage(size: CGSize, color: UIColor) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            color.setFill()
            context.fill(CGRect(origin: .zero, size: size))
            
            // 添加一些细节让图片更真实
            UIColor.white.setFill()
            let detailSize = min(size.width, size.height) * 0.3
            let detailRect = CGRect(
                x: (size.width - detailSize) / 2,
                y: (size.height - detailSize) / 2,
                width: detailSize,
                height: detailSize
            )
            context.fill(detailRect)
        }
    }
}
