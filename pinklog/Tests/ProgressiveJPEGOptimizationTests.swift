import XCTest
@testable import PinkLog
import UIKit

class ProgressiveJPEGOptimizationTests: XCTestCase {
    
    var imageManager: ImageManager!
    var testImage: UIImage!
    var testImageData: Data!
    
    override func setUp() {
        super.setUp()
        imageManager = ImageManager.shared
        
        // 创建测试图片
        let size = CGSize(width: 1000, height: 1000)
        let renderer = UIGraphicsImageRenderer(size: size)
        testImage = renderer.image { context in
            UIColor.blue.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
        
        testImageData = testImage.jpegData(compressionQuality: 0.8)!
    }
    
    override func tearDown() {
        imageManager.clearCache()
        testImage = nil
        testImageData = nil
        super.tearDown()
    }
    
    /// 测试渐进式JPEG质量级别优化
    func testProgressiveJPEGQualityLevelsReduction() {
        let expectation = XCTestExpectation(description: "渐进式JPEG加载完成")
        
        var progressCallCount = 0
        var qualityLevels: [CGFloat] = []
        
        // 测试渐进式JPEG加载
        imageManager.loadProgressiveJPEGAsync(
            data: testImageData,
            key: "test_progressive"
        ) { image, quality in
            progressCallCount += 1
            qualityLevels.append(quality)
        } completion: { finalImage in
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
        
        // 验证质量级别数量已优化
        XCTAssertLessThanOrEqual(progressCallCount, 5, "渐进式JPEG质量级别应不超过5个")
        XCTAssertLessThanOrEqual(qualityLevels.count, 5, "质量级别数组应不超过5个")
        
        // 验证质量级别是预期的值
        let expectedLevels: [CGFloat] = [0.1, 0.3, 0.6, 0.8, 1.0]
        for (index, expectedQuality) in expectedLevels.enumerated() {
            if index < qualityLevels.count {
                XCTAssertEqual(qualityLevels[index], expectedQuality, accuracy: 0.01, "质量级别应匹配预期值")
            }
        }
    }
    
    /// 测试内存占用优化
    func testMemoryUsageOptimization() {
        let initialMemory = getMemoryUsage()
        
        let expectation = XCTestExpectation(description: "多次渐进式JPEG加载完成")
        expectation.expectedFulfillmentCount = 3
        
        // 模拟多次加载，验证内存不会过度增长
        for i in 0..<3 {
            imageManager.loadProgressiveJPEGAsync(
                data: testImageData,
                key: "test_memory_\(i)"
            ) { image, quality in
                // 进度回调
            } completion: { finalImage in
                expectation.fulfill()
            }
        }
        
        wait(for: [expectation], timeout: 10.0)
        
        let finalMemory = getMemoryUsage()
        
        // 验证内存增长在合理范围内（不应该增长太多）
        let memoryIncrease = finalMemory - initialMemory
        XCTAssertLessThan(memoryIncrease, 50.0, "内存增长应控制在50MB以内")
    }
    
    /// 测试渐进式JPEG转换优化
    func testProgressiveJPEGConversionOptimization() {
        // 测试普通图片加载不会自动转换为渐进式JPEG
        let expectation = XCTestExpectation(description: "普通图片加载完成")
        
        imageManager.loadImageAsync(
            data: testImageData,
            key: "test_no_progressive",
            useProgressiveJPEG: true
        ) { image in
            XCTAssertNotNil(image, "图片应该正常加载")
            
            // 验证没有创建渐进式JPEG标记
            let isProgressiveMarked = UserDefaults.standard.bool(forKey: "image_test_no_progressive_is_progressive")
            XCTAssertFalse(isProgressiveMarked, "不应该自动创建渐进式JPEG标记")
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    /// 测试单一质量渐进式JPEG生成
    func testSingleQualityProgressiveJPEG() {
        let progressiveData = imageManager.compressToProgressiveJPEG(testImage, quality: 0.7)
        
        XCTAssertNotNil(progressiveData, "应该能生成渐进式JPEG数据")
        
        if let data = progressiveData {
            // 验证生成的是渐进式JPEG
            XCTAssertTrue(imageManager.isProgressiveJPEG(data), "生成的应该是渐进式JPEG")
            
            // 验证数据大小合理
            XCTAssertGreaterThan(data.count, 0, "渐进式JPEG数据应该有内容")
            XCTAssertLessThan(data.count, testImageData.count * 2, "渐进式JPEG数据不应该过大")
        }
    }
    
    /// 测试缓存优化
    func testCacheOptimization() {
        let expectation = XCTestExpectation(description: "缓存测试完成")
        
        // 第一次加载
        imageManager.loadProgressiveJPEGAsync(
            data: testImageData,
            key: "test_cache"
        ) { image, quality in
            // 进度回调
        } completion: { finalImage in
            XCTAssertNotNil(finalImage, "第一次加载应该成功")
            
            // 第二次加载应该直接从缓存返回
            self.imageManager.loadProgressiveJPEGAsync(
                data: self.testImageData,
                key: "test_cache"
            ) { image, quality in
                XCTFail("缓存命中时不应该有进度回调")
            } completion: { cachedImage in
                XCTAssertNotNil(cachedImage, "缓存加载应该成功")
                expectation.fulfill()
            }
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    /// 测试延迟优化
    func testDelayOptimization() {
        let startTime = Date()
        let expectation = XCTestExpectation(description: "延迟测试完成")
        
        imageManager.loadProgressiveJPEGAsync(
            data: testImageData,
            key: "test_delay"
        ) { image, quality in
            // 进度回调
        } completion: { finalImage in
            let endTime = Date()
            let totalTime = endTime.timeIntervalSince(startTime)
            
            // 验证总时间在合理范围内（5个级别 * 50ms + 处理时间）
            XCTAssertLessThan(totalTime, 1.0, "总加载时间应该在1秒以内")
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    // MARK: - Helper Methods
    
    /// 获取当前内存使用量（MB）
    private func getMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / 1024.0 / 1024.0
        } else {
            return 0.0
        }
    }
}
