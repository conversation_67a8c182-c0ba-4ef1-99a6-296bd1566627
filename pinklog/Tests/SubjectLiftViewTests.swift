import XCTest
import Swift<PERSON>
@testable import PinkLog

class SubjectLiftViewTests: XCTestCase {
    
    var testImage: UIImage!
    var testLiftedImage: UIImage!
    var testStickerImage: UIImage!
    
    override func setUp() {
        super.setUp()
        
        // 创建测试图片
        let size = CGSize(width: 1000, height: 1000)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        testImage = renderer.image { context in
            UIColor.red.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
        
        testLiftedImage = renderer.image { context in
            UIColor.blue.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
        
        testStickerImage = renderer.image { context in
            UIColor.green.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
    }
    
    override func tearDown() {
        testImage = nil
        testLiftedImage = nil
        testStickerImage = nil
        super.tearDown()
    }
    
    /// 测试图片选择和压缩功能
    func testImageSelectionAndCompression() {
        let expectation = XCTestExpectation(description: "图片压缩完成")
        
        var receivedData: Data?
        var receivedImage: UIImage?
        
        // 模拟SubjectLiftView的压缩逻辑
        let mockSubjectLiftView = MockSubjectLiftView(
            originalImage: testImage,
            liftedImage: testLiftedImage,
            stickerImage: testStickerImage
        ) { data, image in
            receivedData = data
            receivedImage = image
            expectation.fulfill()
        }
        
        // 模拟选择贴纸图片
        mockSubjectLiftView.selectAndOptimizeImage(.sticker)
        
        wait(for: [expectation], timeout: 5.0)
        
        XCTAssertNotNil(receivedData, "应该接收到压缩后的数据")
        XCTAssertNotNil(receivedImage, "应该接收到显示用的图片")
        
        if let data = receivedData {
            XCTAssertLessThanOrEqual(data.count, 100 * 1024, "压缩后的数据应小于100KB")
        }
    }
    
    /// 测试内存清理功能
    func testMemoryCleanup() {
        let mockSubjectLiftView = MockSubjectLiftView(
            originalImage: testImage,
            liftedImage: testLiftedImage,
            stickerImage: testStickerImage
        ) { _, _ in }
        
        // 验证初始状态
        XCTAssertNotNil(mockSubjectLiftView.originalImage)
        XCTAssertNotNil(mockSubjectLiftView.liftedImage)
        XCTAssertNotNil(mockSubjectLiftView.stickerImage)
        
        // 选择原图，应该清理其他图片
        mockSubjectLiftView.clearUnselectedImages(keepType: .original)
        
        XCTAssertNotNil(mockSubjectLiftView.originalImage, "原图应该保留")
        XCTAssertNil(mockSubjectLiftView.liftedImage, "抠图应该被清理")
        XCTAssertNil(mockSubjectLiftView.stickerImage, "贴纸应该被清理")
    }
    
    /// 测试不同图片类型的选择
    func testDifferentImageTypeSelection() {
        let expectation = XCTestExpectation(description: "测试不同图片类型")
        expectation.expectedFulfillmentCount = 3
        
        var results: [(ImageType, Data?, UIImage?)] = []
        
        let mockSubjectLiftView = MockSubjectLiftView(
            originalImage: testImage,
            liftedImage: testLiftedImage,
            stickerImage: testStickerImage
        ) { data, image in
            // 这里需要知道是哪种类型，简化测试
            expectation.fulfill()
        }
        
        // 测试选择原图
        mockSubjectLiftView.selectAndOptimizeImage(.original)
        
        // 重置图片
        mockSubjectLiftView.originalImage = testImage
        mockSubjectLiftView.liftedImage = testLiftedImage
        mockSubjectLiftView.stickerImage = testStickerImage
        
        // 测试选择抠图
        mockSubjectLiftView.selectAndOptimizeImage(.lifted)
        
        // 重置图片
        mockSubjectLiftView.originalImage = testImage
        mockSubjectLiftView.liftedImage = testLiftedImage
        mockSubjectLiftView.stickerImage = testStickerImage
        
        // 测试选择贴纸
        mockSubjectLiftView.selectAndOptimizeImage(.sticker)
        
        wait(for: [expectation], timeout: 10.0)
    }
}

// MARK: - Mock Classes

/// 模拟SubjectLiftView的核心逻辑
class MockSubjectLiftView {
    var originalImage: UIImage?
    var liftedImage: UIImage?
    var stickerImage: UIImage?
    
    private let onImageProcessed: (Data, UIImage) -> Void
    
    enum ImageType {
        case original, lifted, sticker
    }
    
    init(originalImage: UIImage?, liftedImage: UIImage?, stickerImage: UIImage?, onImageProcessed: @escaping (Data, UIImage) -> Void) {
        self.originalImage = originalImage
        self.liftedImage = liftedImage
        self.stickerImage = stickerImage
        self.onImageProcessed = onImageProcessed
    }
    
    func selectAndOptimizeImage(_ imageType: ImageType) {
        guard let selectedImage = getImageForType(imageType) else {
            return
        }
        
        // 后台线程进行压缩处理
        DispatchQueue.global(qos: .userInitiated).async {
            autoreleasepool {
                // 使用ImageManager压缩图片
                let compressedData = ImageManager.shared.compressToTargetSize(
                    selectedImage,
                    maxSize: 512,
                    quality: 0.3,
                    targetBytes: 100 * 1024
                )
                
                DispatchQueue.main.async {
                    guard let data = compressedData else {
                        return
                    }
                    
                    // 立即清理其他未选择的图片引用
                    self.clearUnselectedImages(keepType: imageType)
                    
                    // 触发回调
                    self.onImageProcessed(data, selectedImage)
                }
            }
        }
    }
    
    private func getImageForType(_ imageType: ImageType) -> UIImage? {
        switch imageType {
        case .original:
            return originalImage
        case .lifted:
            return liftedImage
        case .sticker:
            return stickerImage
        }
    }
    
    func clearUnselectedImages(keepType: ImageType) {
        switch keepType {
        case .original:
            liftedImage = nil
            stickerImage = nil
        case .lifted:
            originalImage = nil
            stickerImage = nil
        case .sticker:
            originalImage = nil
            liftedImage = nil
        }
    }
}
