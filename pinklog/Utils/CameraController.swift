//
//  CameraController.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import UIKit
import AVFoundation
import SwiftUI

class CameraController: UIViewController {
    private var captureSession: AVCaptureSession?
    private var videoPreviewLayer: AVCaptureVideoPreviewLayer?
    private var photoOutput: AVCapturePhotoOutput?

    var onImageCaptured: ((UIImage) -> Void)?
    var onAutoProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)? // 原图, 抠图, 贴纸

    deinit {
        // 🔥 确保相机资源被正确释放
        captureSession?.stopRunning()
        captureSession = nil
        videoPreviewLayer?.removeFromSuperlayer()
        videoPreviewLayer = nil
        photoOutput = nil
        onImageCaptured = nil
        onAutoProcessCompleted = nil
        print("🗑️ CameraController已释放")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupCamera()
        setupUI()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        videoPreviewLayer?.frame = view.bounds
    }
    
    private func setupCamera() {
        captureSession = AVCaptureSession()
        guard let captureSession = captureSession else { return }
        
        // 配置会话质量
        captureSession.sessionPreset = .photo
        
        // 设置输入设备
        guard let backCamera = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else {
            print("无法访问后置摄像头")
            return
        }
        
        do {
            let input = try AVCaptureDeviceInput(device: backCamera)
            if captureSession.canAddInput(input) {
                captureSession.addInput(input)
            }
        } catch {
            print("相机输入设置错误: \(error)")
            return
        }
        
        // 设置照片输出
        photoOutput = AVCapturePhotoOutput()
        if let photoOutput = photoOutput, captureSession.canAddOutput(photoOutput) {
            captureSession.addOutput(photoOutput)
        }
        
        // 设置预览层
        videoPreviewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
        videoPreviewLayer?.videoGravity = .resizeAspectFill
        videoPreviewLayer?.frame = view.bounds
        
        if let videoPreviewLayer = videoPreviewLayer {
            view.layer.addSublayer(videoPreviewLayer)
        }
        
        // 启动会话
        DispatchQueue.global(qos: .userInitiated).async {
            captureSession.startRunning()
        }
    }
    
    private func setupUI() {
        // 添加拍照按钮
        let captureButton = UIButton(type: .system)
        captureButton.backgroundColor = .white
        captureButton.layer.cornerRadius = 35
        captureButton.frame = CGRect(x: 0, y: 0, width: 70, height: 70)
        captureButton.center = CGPoint(x: view.center.x, y: view.bounds.height - 100)
        captureButton.addTarget(self, action: #selector(capturePhoto), for: .touchUpInside)
        
        // 添加拍照图标
        let cameraImage = UIImage(systemName: "camera.fill")
        captureButton.setImage(cameraImage, for: .normal)
        captureButton.tintColor = .black
        captureButton.imageView?.contentMode = .scaleAspectFit
        captureButton.contentVerticalAlignment = .fill
        captureButton.contentHorizontalAlignment = .fill
        captureButton.imageEdgeInsets = UIEdgeInsets(top: 20, left: 20, bottom: 20, right: 20)
        
        view.addSubview(captureButton)
        
        // 添加关闭按钮
        let closeButton = UIButton(type: .system)
        closeButton.setImage(UIImage(systemName: "xmark"), for: .normal)
        closeButton.tintColor = .white
        closeButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        closeButton.layer.cornerRadius = 20
        closeButton.frame = CGRect(x: 30, y: 50, width: 40, height: 40)
        closeButton.addTarget(self, action: #selector(dismissCamera), for: .touchUpInside)
        view.addSubview(closeButton)
        
        // 添加切换到相册按钮
        let libraryButton = UIButton(type: .system)
        libraryButton.setImage(UIImage(systemName: "photo.on.rectangle"), for: .normal)
        libraryButton.tintColor = .white
        libraryButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        libraryButton.layer.cornerRadius = 20
        libraryButton.frame = CGRect(x: view.bounds.width - 70, y: 50, width: 40, height: 40)
        libraryButton.addTarget(self, action: #selector(openPhotoLibrary), for: .touchUpInside)
        view.addSubview(libraryButton)
    }
    
    @objc private func capturePhoto() {
        guard let photoOutput = photoOutput else { return }
        
        let photoSettings = AVCapturePhotoSettings()
        photoOutput.capturePhoto(with: photoSettings, delegate: self)
    }
    
    @objc private func dismissCamera() {
        dismiss(animated: true)
    }
    
    @objc private func openPhotoLibrary() {
        // 这里可以触发相册选择
        // 暂时先关闭相机，让用户使用系统的PhotosPicker
        dismiss(animated: true)
    }

    func startAutoProcessing(originalImage: UIImage, callback: @escaping (UIImage, UIImage, UIImage) -> Void) {
        autoreleasepool {
            // 记录内存使用情况
            logMemoryUsage("CameraController开始自动处理")

            // 显示处理指示器
            showProcessingIndicator()

            SubjectLiftManager.shared.autoProcessImage(from: originalImage) { result in
                autoreleasepool {
                    DispatchQueue.main.async {
                        self.hideProcessingIndicator()

                        switch result {
                        case .success(let (liftedImage, stickerImage)):
                            callback(originalImage, liftedImage, stickerImage)
                            self.dismiss(animated: true)
                        case .failure(_):
                            // 处理失败，返回原图
                            callback(originalImage, originalImage, originalImage)
                            self.dismiss(animated: true)
                        }

                        // 记录内存使用情况
                        self.logMemoryUsage("CameraController自动处理完成")
                    }
                }
            }
        }
    }

    func showProcessingIndicator() {
        let indicator = UIActivityIndicatorView(style: .large)
        indicator.color = .white
        indicator.tag = 999
        indicator.center = view.center
        indicator.startAnimating()
        view.addSubview(indicator)

        let label = UILabel()
        label.text = "正在处理图片..."
        label.textColor = .white
        label.font = .systemFont(ofSize: 16)
        label.tag = 998
        label.textAlignment = .center
        label.frame = CGRect(x: 0, y: indicator.frame.maxY + 10, width: view.bounds.width, height: 30)
        view.addSubview(label)
    }

    func hideProcessingIndicator() {
        view.viewWithTag(999)?.removeFromSuperview()
        view.viewWithTag(998)?.removeFromSuperview()
    }

    /// 监控内存使用情况（调试用）
    private func logMemoryUsage(_ context: String) {
        MemoryMonitor.shared.recordMemoryUsage("CameraController: \(context)")
    }
}

// MARK: - AVCapturePhotoCaptureDelegate
extension CameraController: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        guard error == nil else {
            print("拍照错误: \(error!)")
            return
        }

        logMemoryUsage("拍照完成，开始处理照片数据")

        guard let imageData = photo.fileDataRepresentation() else {
            print("无法获取照片数据")
            return
        }

        // 🔥 关键修复：在后台线程处理图片，避免主线程内存峰值
        DispatchQueue.global(qos: .userInitiated).async {
            let compressedImage: UIImage = autoreleasepool {
                guard let image = UIImage(data: imageData) else {
                    print("无法处理照片数据")
                    return UIImage() // 返回空图片作为fallback
                }

                print("📸 原图尺寸: \(image.size)")
                self.logMemoryUsage("压缩前")

                // 🔥 立即压缩并释放原图
                let compressed = ImageManager.shared.compressImage(image, maxSize: 512, quality: 0.5)
                print("📸 压缩后尺寸: \(compressed.size)")

                return compressed
                // 原图在这里自动释放
            }

            // 🔥 强制内存清理
            autoreleasepool {
                ImageManager.shared.clearCache()
            }

            self.logMemoryUsage("照片数据处理完成")

            DispatchQueue.main.async {
                // 如果设置了自动处理回调，则进行自动抠图和贴纸化
                if let autoProcessCallback = self.onAutoProcessCompleted {
                    // 🔥 传递压缩后的图片，而不是原始大图片
                    self.startAutoProcessing(originalImage: compressedImage, callback: autoProcessCallback)
                } else {
                    // 否则使用原有的简单回调
                    self.onImageCaptured?(compressedImage)
                    self.dismiss(animated: true)
                }
            }
        }
    }
}

// MARK: - SwiftUI集成
struct CameraView: UIViewControllerRepresentable {
    @Binding var capturedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode

    func makeUIViewController(context: Context) -> CameraController {
        let controller = CameraController()
        controller.onImageCaptured = { image in
            capturedImage = image
        }
        return controller
    }

    func updateUIViewController(_ uiViewController: CameraController, context: Context) {}
}

// MARK: - 自动处理版本的CameraView
struct AutoProcessCameraView: UIViewControllerRepresentable {
    var onProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)?
    @Environment(\.presentationMode) var presentationMode

    func makeUIViewController(context: Context) -> CameraController {
        let controller = CameraController()
        controller.onAutoProcessCompleted = onProcessCompleted
        return controller
    }

    func updateUIViewController(_ uiViewController: CameraController, context: Context) {}
}
