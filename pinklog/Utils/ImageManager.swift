import Foundation
import UIKit
import SwiftUI
import ImageIO
import UniformTypeIdentifiers
import CoreServices
import Darwin



/// 图片管理工具类，负责图片的压缩、缓存和异步加载
class ImageManager {
    // MARK: - 单例
    static let shared = ImageManager()

    // MARK: - 属性

    /// 内存缓存
    private let imageCache = NSCache<NSString, UIImage>()

    /// 缩略图缓存
    private let thumbnailCache = NSCache<NSString, UIImage>()

    /// 已完全加载的图片键集合
    private let fullyLoadedKeys = NSMutableSet()

    /// 磁盘缓存目录
    private let cachesDirectory: URL

    // MARK: - 初始化

    private init() {
        // 设置缓存容量
        imageCache.countLimit = 100
        thumbnailCache.countLimit = 200

        // 设置缓存目录
        let fileManager = FileManager.default
        cachesDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask)[0].appendingPathComponent("ImageCache")

        // 创建缓存目录
        if !fileManager.fileExists(atPath: cachesDirectory.path) {
            do {
                try fileManager.createDirectory(at: cachesDirectory, withIntermediateDirectories: true)
            } catch {
                print("创建缓存目录失败: \(error)")
            }
        }
    }

    // MARK: - 图片压缩

    /// 压缩图片到指定大小 - 内存优化版本
    /// - Parameters:
    ///   - image: 原始图片
    ///   - maxSize: 最大尺寸（宽度或高度）
    ///   - quality: 压缩质量 (0.0-1.0)
    /// - Returns: 压缩后的图片
    func compressImage(_ image: UIImage, maxSize: CGFloat = 1024, quality: CGFloat = 0.7) -> UIImage {
        return autoreleasepool {
            // 计算缩放比例
            let originalSize = image.size
            var scale: CGFloat = 1.0

            if originalSize.width > maxSize || originalSize.height > maxSize {
                let widthScale = maxSize / originalSize.width
                let heightScale = maxSize / originalSize.height
                scale = min(widthScale, heightScale)
            }

            // 如果不需要缩放，直接返回原图
            if scale >= 1.0 {
                return image
            }

            // 计算新尺寸
            let newSize = CGSize(width: originalSize.width * scale, height: originalSize.height * scale)

            // 🔥 使用更内存友好的方式：直接创建Data然后转换为UIImage
            // 这样避免了UIGraphicsImageRenderer同时持有两个图片的问题
            guard let cgImage = image.cgImage else { return image }

            // 创建位图上下文
            let colorSpace = CGColorSpaceCreateDeviceRGB()
            let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.premultipliedLast.rawValue)

            guard let context = CGContext(
                data: nil,
                width: Int(newSize.width),
                height: Int(newSize.height),
                bitsPerComponent: 8,
                bytesPerRow: 0,
                space: colorSpace,
                bitmapInfo: bitmapInfo.rawValue
            ) else { return image }

            // 绘制缩放后的图片
            context.interpolationQuality = .high
            context.draw(cgImage, in: CGRect(origin: .zero, size: newSize))

            // 创建新的CGImage
            guard let scaledCGImage = context.makeImage() else { return image }

            // 创建UIImage，保持原始图片的方向
            let scaledImage = UIImage(cgImage: scaledCGImage, scale: image.scale, orientation: image.imageOrientation)

            return scaledImage
        }
    }

    /// 生成缩略图
    /// - Parameters:
    ///   - image: 原始图片
    ///   - size: 缩略图尺寸
    /// - Returns: 缩略图
    func generateThumbnail(_ image: UIImage, size: CGFloat = 200) -> UIImage {
        return compressImage(image, maxSize: size, quality: 0.5)
    }

    /// 处理透明背景图片，确保输出小于100KB
    /// - Parameter image: 带透明背景的图片
    /// - Returns: 处理后的图片数据
    func processTransparentImage(_ image: UIImage) -> Data? {
        return autoreleasepool {
            // 使用新的目标大小压缩方法，确保小于100KB
            return compressToTargetSize(image, maxSize: 512, quality: 0.3, targetBytes: 100 * 1024)
        }
    }

    /// 检查图片是否有透明背景
    /// - Parameter image: 要检查的图片
    /// - Returns: 是否有透明背景
    private func hasTransparentBackground(_ image: UIImage) -> Bool {
        guard let cgImage = image.cgImage else { return false }

        // 检查图片的alpha信息
        let alphaInfo = cgImage.alphaInfo
        switch alphaInfo {
        case .none, .noneSkipFirst, .noneSkipLast:
            return false
        default:
            return true
        }
    }

    /// 压缩图片数据
    /// - Parameters:
    ///   - image: 原始图片
    ///   - quality: 压缩质量 (0.0-1.0)
    /// - Returns: 压缩后的数据
    func compressImageData(_ image: UIImage, quality: CGFloat = 0.7) -> Data? {
        return image.jpegData(compressionQuality: quality)
    }

    /// 压缩图片到目标大小，确保输出小于指定字节数
    /// - Parameters:
    ///   - image: 原始图片
    ///   - maxSize: 最大尺寸（宽度或高度），默认512px
    ///   - quality: 初始压缩质量，默认0.3
    ///   - targetBytes: 目标字节数，默认100KB
    /// - Returns: 压缩后的图片数据
    func compressToTargetSize(_ image: UIImage, maxSize: CGFloat = 512, quality: CGFloat = 0.3, targetBytes: Int = 100 * 1024) -> Data? {
        return autoreleasepool {
            // 1. 首先压缩尺寸
            let resizedImage = compressImage(image, maxSize: maxSize)

            // 2. 检查是否有透明背景，决定使用PNG还是JPEG
            let hasTransparency = hasTransparentBackground(resizedImage)

            if hasTransparency {
                // 对于透明图片，使用PNG格式
                guard let pngData = resizedImage.pngData() else { return nil }

                // 如果PNG数据已经小于目标大小，直接返回
                if pngData.count <= targetBytes {
                    return pngData
                }

                // PNG无法进一步压缩质量，只能进一步缩小尺寸
                let scaleFactor = sqrt(Double(targetBytes) / Double(pngData.count))
                let newMaxSize = maxSize * CGFloat(scaleFactor)
                let furtherResized = compressImage(resizedImage, maxSize: max(newMaxSize, 128)) // 最小128px
                return furtherResized.pngData()
            } else {
                // 对于不透明图片，使用JPEG格式并循环压缩
                var currentQuality = quality
                var currentData: Data?

                // 最多尝试10次压缩
                for _ in 0..<10 {
                    currentData = resizedImage.jpegData(compressionQuality: currentQuality)

                    guard let data = currentData else { break }

                    // 如果达到目标大小，返回结果
                    if data.count <= targetBytes {
                        return data
                    }

                    // 降低质量继续压缩
                    currentQuality *= 0.8

                    // 如果质量太低，停止压缩
                    if currentQuality < 0.1 {
                        break
                    }
                }

                // 如果仍然太大，进一步缩小尺寸
                if let data = currentData, data.count > targetBytes {
                    let scaleFactor = sqrt(Double(targetBytes) / Double(data.count))
                    let newMaxSize = maxSize * CGFloat(scaleFactor)
                    let furtherResized = compressImage(resizedImage, maxSize: max(newMaxSize, 128)) // 最小128px
                    return furtherResized.jpegData(compressionQuality: 0.3)
                }

                return currentData
            }
        }
    }

    /// 批量清理UIImage引用，释放内存
    /// - Parameter images: 要清理的图片引用数组
    func clearImageReferences(_ images: inout [UIImage?]) {
        autoreleasepool {
            for i in 0..<images.count {
                images[i] = nil
            }
            images.removeAll()
        }

        // 强制内存回收
        DispatchQueue.global(qos: .utility).async {
            autoreleasepool {
                // 触发内存清理
            }
        }
    }

    /// 清理单个图片引用
    /// - Parameter image: 要清理的图片引用
    func clearImageReference(_ image: inout UIImage?) {
        autoreleasepool {
            image = nil
        }
    }

    /// 获取当前内存使用情况（用于监控）
    /// - Returns: 内存使用情况的描述
    func getMemoryUsage() -> String {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }

        if kerr == KERN_SUCCESS {
            let usedMB = Double(info.resident_size) / 1024.0 / 1024.0
            return String(format: "内存使用: %.1f MB", usedMB)
        } else {
            return "无法获取内存信息"
        }
    }

    /// 清理临时缓存，释放内存
    func clearTemporaryCache() {
        autoreleasepool {
            // 清理图片缓存
            clearCache()

            // 清理UserDefaults中的临时数据
            let keys = UserDefaults.standard.dictionaryRepresentation().keys
            for key in keys {
                if key.hasPrefix("image_") && (key.hasSuffix("_is_progressive") || key.hasSuffix("_progressive")) {
                    UserDefaults.standard.removeObject(forKey: key)
                }
            }

            // 强制内存回收
            DispatchQueue.global(qos: .utility).async {
                autoreleasepool {
                    // 触发内存清理
                }
            }
        }
    }

    // MARK: - 渐进式JPEG







    // MARK: - 缓存管理

    /// 从缓存中获取图片
    /// - Parameter key: 缓存键
    /// - Returns: 缓存的图片，如果不存在则返回nil
    func getImageFromCache(key: String) -> UIImage? {
        return imageCache.object(forKey: key as NSString)
    }

    /// 从缓存中获取缩略图
    /// - Parameter key: 缓存键
    /// - Returns: 缓存的缩略图，如果不存在则返回nil
    func getThumbnailFromCache(key: String) -> UIImage? {
        return thumbnailCache.object(forKey: key as NSString)
    }

    /// 将图片存入缓存
    /// - Parameters:
    ///   - image: 图片
    ///   - key: 缓存键
    func cacheImage(_ image: UIImage, key: String) {
        imageCache.setObject(image, forKey: key as NSString)
    }

    /// 将缩略图存入缓存
    /// - Parameters:
    ///   - thumbnail: 缩略图
    ///   - key: 缓存键
    func cacheThumbnail(_ thumbnail: UIImage, key: String) {
        thumbnailCache.setObject(thumbnail, forKey: key as NSString)
    }

    /// 清除所有缓存
    func clearCache() {
        imageCache.removeAllObjects()
        thumbnailCache.removeAllObjects()
        fullyLoadedKeys.removeAllObjects()
    }
    
    /// 清除临时缓存，只清除不常用的项目
    /// 比全部清理更加精细化的缓存管理
    func clearLightweightCache() {
        // 清除超过30个项目时，清理多余的项目
        if imageCache.countLimit > 30 {
            imageCache.countLimit = 30
        }

        // 清除超过50个项目时，清理多余的缩略图
        if thumbnailCache.countLimit > 50 {
            thumbnailCache.countLimit = 50
        }
    }

    /// 检查是否已有高质量缓存
    /// - Parameter key: 缓存键
    /// - Returns: 是否已有高质量缓存
    func hasHighQualityCache(key: String) -> Bool {
        return getImageFromCache(key: key) != nil && isFullyLoaded(key: key)
    }

    /// 标记图片为已完全加载
    /// - Parameter key: 缓存键
    func markAsFullyLoaded(key: String) {
        fullyLoadedKeys.add(key)
    }

    /// 检查图片是否已完全加载
    /// - Parameter key: 缓存键
    /// - Returns: 是否已完全加载
    func isFullyLoaded(key: String) -> Bool {
        return fullyLoadedKeys.contains(key)
    }

    // MARK: - 异步图片加载

    /// 异步加载图片
    /// - Parameters:
    ///   - data: 图片数据
    ///   - key: 缓存键
    ///   - completion: 完成回调
    func loadImageAsync(data: Data, key: String, completion: @escaping (UIImage?) -> Void) {
        // 检查缓存
        if let cachedImage = getImageFromCache(key: key) {
            completion(cachedImage)
            return
        }

        // 后台线程加载图片
        DispatchQueue.global(qos: .userInitiated).async {
            autoreleasepool {
                guard let image = UIImage(data: data) else {
                    DispatchQueue.main.async {
                        completion(nil)
                    }
                    return
                }

                // 压缩图片
                let compressedImage = self.compressImage(image)

                // 缓存图片
                self.cacheImage(compressedImage, key: key)

                // 生成并缓存缩略图
                let thumbnail = self.generateThumbnail(image)
                self.cacheThumbnail(thumbnail, key: key + "_thumb")

                // 主线程返回结果
                DispatchQueue.main.async {
                    // 标记为已完全加载
                    self.markAsFullyLoaded(key: key)
                    completion(compressedImage)
                }

                // 图片处理完成
            }
        }
    }

    /// 异步加载缩略图
    /// - Parameters:
    ///   - data: 图片数据
    ///   - key: 缓存键
    ///   - completion: 完成回调
    func loadThumbnailAsync(data: Data, key: String, completion: @escaping (UIImage?) -> Void) {
        // 检查缓存
        if let cachedThumbnail = getThumbnailFromCache(key: key + "_thumb") {
            completion(cachedThumbnail)
            return
        }

        // 后台线程加载图片
        DispatchQueue.global(qos: .userInitiated).async {
            autoreleasepool {
                guard let image = UIImage(data: data) else {
                    DispatchQueue.main.async {
                        completion(nil)
                    }
                    return
                }

                // 生成缩略图
                let thumbnail = self.generateThumbnail(image)

                // 缓存缩略图
                self.cacheThumbnail(thumbnail, key: key + "_thumb")

                // 主线程返回结果
                DispatchQueue.main.async {
                    completion(thumbnail)
                }
            }
        }
    }


}
