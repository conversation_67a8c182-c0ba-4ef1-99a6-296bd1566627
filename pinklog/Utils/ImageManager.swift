import Foundation
import UIKit
import SwiftUI
import ImageIO
import UniformTypeIdentifiers
import CoreServices
import Darwin

// 定义JPEG相关常量 - 使用字符串常量，避免直接引用可能不存在的符号
private let kCGImagePropertyJPEGDictionary = "kCGImagePropertyJPEGDictionary" as CFString
private let kCGImagePropertyJPEGIsProgressiveKey = "kCGImagePropertyJPEGIsProgressive" as CFString

/// 图片管理工具类，负责图片的压缩、缓存和异步加载
class ImageManager {
    // MARK: - 单例
    static let shared = ImageManager()

    // MARK: - 属性

    /// 内存缓存
    private let imageCache = NSCache<NSString, UIImage>()

    /// 缩略图缓存
    private let thumbnailCache = NSCache<NSString, UIImage>()

    /// 已完全加载的图片键集合
    private let fullyLoadedKeys = NSMutableSet()

    /// 磁盘缓存目录
    private let cachesDirectory: URL

    // MARK: - 初始化

    private init() {
        // 设置缓存容量
        imageCache.countLimit = 100
        thumbnailCache.countLimit = 200

        // 设置缓存目录
        let fileManager = FileManager.default
        cachesDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask)[0].appendingPathComponent("ImageCache")

        // 创建缓存目录
        if !fileManager.fileExists(atPath: cachesDirectory.path) {
            do {
                try fileManager.createDirectory(at: cachesDirectory, withIntermediateDirectories: true)
            } catch {
                print("创建缓存目录失败: \(error)")
            }
        }
    }

    // MARK: - 图片压缩

    /// 压缩图片到指定大小
    /// - Parameters:
    ///   - image: 原始图片
    ///   - maxSize: 最大尺寸（宽度或高度）
    ///   - quality: 压缩质量 (0.0-1.0)
    /// - Returns: 压缩后的图片
    func compressImage(_ image: UIImage, maxSize: CGFloat = 1024, quality: CGFloat = 0.7) -> UIImage {
        return autoreleasepool {
            // 计算缩放比例
            let originalSize = image.size
            var scale: CGFloat = 1.0

            if originalSize.width > maxSize || originalSize.height > maxSize {
                let widthScale = maxSize / originalSize.width
                let heightScale = maxSize / originalSize.height
                scale = min(widthScale, heightScale)
            }

            // 如果不需要缩放，直接返回原图
            if scale >= 1.0 {
                return image
            }

            // 计算新尺寸
            let newSize = CGSize(width: originalSize.width * scale, height: originalSize.height * scale)

            // 使用更高效的图片渲染方式
            let renderer = UIGraphicsImageRenderer(size: newSize)
            let scaledImage = renderer.image { _ in
                image.draw(in: CGRect(origin: .zero, size: newSize))
            }

            return scaledImage
        }
    }

    /// 生成缩略图
    /// - Parameters:
    ///   - image: 原始图片
    ///   - size: 缩略图尺寸
    /// - Returns: 缩略图
    func generateThumbnail(_ image: UIImage, size: CGFloat = 200) -> UIImage {
        return compressImage(image, maxSize: size, quality: 0.5)
    }

    /// 处理透明背景图片，确保输出小于100KB
    /// - Parameter image: 带透明背景的图片
    /// - Returns: 处理后的图片数据
    func processTransparentImage(_ image: UIImage) -> Data? {
        return autoreleasepool {
            // 使用新的目标大小压缩方法，确保小于100KB
            return compressToTargetSize(image, maxSize: 512, quality: 0.3, targetBytes: 100 * 1024)
        }
    }

    /// 检查图片是否有透明背景
    /// - Parameter image: 要检查的图片
    /// - Returns: 是否有透明背景
    private func hasTransparentBackground(_ image: UIImage) -> Bool {
        guard let cgImage = image.cgImage else { return false }

        // 检查图片的alpha信息
        let alphaInfo = cgImage.alphaInfo
        switch alphaInfo {
        case .none, .noneSkipFirst, .noneSkipLast:
            return false
        default:
            return true
        }
    }

    /// 压缩图片数据
    /// - Parameters:
    ///   - image: 原始图片
    ///   - quality: 压缩质量 (0.0-1.0)
    /// - Returns: 压缩后的数据
    func compressImageData(_ image: UIImage, quality: CGFloat = 0.7) -> Data? {
        return image.jpegData(compressionQuality: quality)
    }

    /// 压缩图片到目标大小，确保输出小于指定字节数
    /// - Parameters:
    ///   - image: 原始图片
    ///   - maxSize: 最大尺寸（宽度或高度），默认512px
    ///   - quality: 初始压缩质量，默认0.3
    ///   - targetBytes: 目标字节数，默认100KB
    /// - Returns: 压缩后的图片数据
    func compressToTargetSize(_ image: UIImage, maxSize: CGFloat = 512, quality: CGFloat = 0.3, targetBytes: Int = 100 * 1024) -> Data? {
        return autoreleasepool {
            // 1. 首先压缩尺寸
            let resizedImage = compressImage(image, maxSize: maxSize)

            // 2. 检查是否有透明背景，决定使用PNG还是JPEG
            let hasTransparency = hasTransparentBackground(resizedImage)

            if hasTransparency {
                // 对于透明图片，使用PNG格式
                guard let pngData = resizedImage.pngData() else { return nil }

                // 如果PNG数据已经小于目标大小，直接返回
                if pngData.count <= targetBytes {
                    return pngData
                }

                // PNG无法进一步压缩质量，只能进一步缩小尺寸
                let scaleFactor = sqrt(Double(targetBytes) / Double(pngData.count))
                let newMaxSize = maxSize * CGFloat(scaleFactor)
                let furtherResized = compressImage(resizedImage, maxSize: max(newMaxSize, 128)) // 最小128px
                return furtherResized.pngData()
            } else {
                // 对于不透明图片，使用JPEG格式并循环压缩
                var currentQuality = quality
                var currentData: Data?

                // 最多尝试10次压缩
                for _ in 0..<10 {
                    currentData = resizedImage.jpegData(compressionQuality: currentQuality)

                    guard let data = currentData else { break }

                    // 如果达到目标大小，返回结果
                    if data.count <= targetBytes {
                        return data
                    }

                    // 降低质量继续压缩
                    currentQuality *= 0.8

                    // 如果质量太低，停止压缩
                    if currentQuality < 0.1 {
                        break
                    }
                }

                // 如果仍然太大，进一步缩小尺寸
                if let data = currentData, data.count > targetBytes {
                    let scaleFactor = sqrt(Double(targetBytes) / Double(data.count))
                    let newMaxSize = maxSize * CGFloat(scaleFactor)
                    let furtherResized = compressImage(resizedImage, maxSize: max(newMaxSize, 128)) // 最小128px
                    return furtherResized.jpegData(compressionQuality: 0.3)
                }

                return currentData
            }
        }
    }

    /// 批量清理UIImage引用，释放内存
    /// - Parameter images: 要清理的图片引用数组
    func clearImageReferences(_ images: inout [UIImage?]) {
        autoreleasepool {
            for i in 0..<images.count {
                images[i] = nil
            }
            images.removeAll()
        }

        // 强制内存回收
        DispatchQueue.global(qos: .utility).async {
            autoreleasepool {
                // 触发内存清理
            }
        }
    }

    /// 清理单个图片引用
    /// - Parameter image: 要清理的图片引用
    func clearImageReference(_ image: inout UIImage?) {
        autoreleasepool {
            image = nil
        }
    }

    /// 获取当前内存使用情况（用于监控）
    /// - Returns: 内存使用情况的描述
    func getMemoryUsage() -> String {
        let info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }

        if kerr == KERN_SUCCESS {
            let usedMB = Double(info.resident_size) / 1024.0 / 1024.0
            return String(format: "内存使用: %.1f MB", usedMB)
        } else {
            return "无法获取内存信息"
        }
    }

    // MARK: - 渐进式JPEG

    /// 将图片压缩为渐进式JPEG格式
    /// - Parameters:
    ///   - image: 原始图片
    ///   - quality: 压缩质量 (0.0-1.0)
    /// - Returns: 渐进式JPEG数据
    func compressToProgressiveJPEG(_ image: UIImage, quality: CGFloat = 0.7) -> Data? {
        // 先压缩图片尺寸
        let compressedImage = compressImage(image)

        // 创建渐进式JPEG数据
        let outputData = NSMutableData()

        // 创建图像目标
        let jpegUTType: CFString
        if #available(iOS 15.0, *) {
            jpegUTType = UTType.jpeg.identifier as CFString
        } else {
            jpegUTType = kUTTypeJPEG
        }

        guard let destination = CGImageDestinationCreateWithData(
            outputData as CFMutableData,
            jpegUTType,
            1,
            nil
        ) else {
            print("创建图像目标失败")
            return compressedImage.jpegData(compressionQuality: quality)
        }

        // 设置渐进式JPEG选项
        let options: [CFString: Any] = [
            kCGImageDestinationLossyCompressionQuality: quality,
            kCGImagePropertyJPEGIsProgressiveKey: true
        ]

        // 添加图像
        guard let cgImage = compressedImage.cgImage else {
            print("获取CGImage失败")
            return compressedImage.jpegData(compressionQuality: quality)
        }

        CGImageDestinationAddImage(destination, cgImage, options as CFDictionary)

        // 完成图像创建
        if CGImageDestinationFinalize(destination) {
            return outputData as Data
        } else {
            print("渐进式JPEG创建失败")
            return compressedImage.jpegData(compressionQuality: quality)
        }
    }

    /// 检查数据是否为渐进式JPEG
    /// - Parameter data: 图片数据
    /// - Returns: 是否为渐进式JPEG
    func isProgressiveJPEG(_ data: Data) -> Bool {
        // 创建图像源
        guard let source = CGImageSourceCreateWithData(data as CFData, nil) else {
            return false
        }

        // 获取图像属性
        guard let properties = CGImageSourceCopyPropertiesAtIndex(source, 0, nil) as? [CFString: Any] else {
            return false
        }

        // 获取JPEG属性
        guard let jpegProperties = properties[kCGImagePropertyJPEGDictionary] as? [CFString: Any] else {
            return false
        }

        // 检查是否为渐进式JPEG
        return jpegProperties[kCGImagePropertyJPEGIsProgressiveKey] as? Bool ?? false
    }

    /// 将普通JPEG转换为渐进式JPEG
    /// - Parameters:
    ///   - data: 图片数据
    ///   - quality: 压缩质量 (0.0-1.0)
    /// - Returns: 渐进式JPEG数据
    func convertToProgressiveJPEG(_ data: Data, quality: CGFloat = 0.7) -> Data? {
        // 如果已经是渐进式JPEG，直接返回
        if isProgressiveJPEG(data) {
            return data
        }

        // 创建UIImage
        guard let image = UIImage(data: data) else {
            return nil
        }

        // 转换为渐进式JPEG
        return compressToProgressiveJPEG(image, quality: quality)
    }

    // MARK: - 缓存管理

    /// 从缓存中获取图片
    /// - Parameter key: 缓存键
    /// - Returns: 缓存的图片，如果不存在则返回nil
    func getImageFromCache(key: String) -> UIImage? {
        return imageCache.object(forKey: key as NSString)
    }

    /// 从缓存中获取缩略图
    /// - Parameter key: 缓存键
    /// - Returns: 缓存的缩略图，如果不存在则返回nil
    func getThumbnailFromCache(key: String) -> UIImage? {
        return thumbnailCache.object(forKey: key as NSString)
    }

    /// 将图片存入缓存
    /// - Parameters:
    ///   - image: 图片
    ///   - key: 缓存键
    func cacheImage(_ image: UIImage, key: String) {
        imageCache.setObject(image, forKey: key as NSString)
    }

    /// 将缩略图存入缓存
    /// - Parameters:
    ///   - thumbnail: 缩略图
    ///   - key: 缓存键
    func cacheThumbnail(_ thumbnail: UIImage, key: String) {
        thumbnailCache.setObject(thumbnail, forKey: key as NSString)
    }

    /// 清除所有缓存
    func clearCache() {
        imageCache.removeAllObjects()
        thumbnailCache.removeAllObjects()
        fullyLoadedKeys.removeAllObjects()
    }
    
    /// 清除临时缓存，只清除不常用的项目
    /// 比全部清理更加精细化的缓存管理
    func clearTemporaryCache() {
        // 清除超过30个项目时，清理多余的项目
        if imageCache.countLimit > 30 {
            imageCache.countLimit = 30
        }
        
        // 清除超过50个项目时，清理多余的缩略图
        if thumbnailCache.countLimit > 50 {
            thumbnailCache.countLimit = 50
        }
    }

    /// 检查是否已有高质量缓存
    /// - Parameter key: 缓存键
    /// - Returns: 是否已有高质量缓存
    func hasHighQualityCache(key: String) -> Bool {
        return getImageFromCache(key: key) != nil && isFullyLoaded(key: key)
    }

    /// 标记图片为已完全加载
    /// - Parameter key: 缓存键
    func markAsFullyLoaded(key: String) {
        fullyLoadedKeys.add(key)
    }

    /// 检查图片是否已完全加载
    /// - Parameter key: 缓存键
    /// - Returns: 是否已完全加载
    func isFullyLoaded(key: String) -> Bool {
        return fullyLoadedKeys.contains(key)
    }

    // MARK: - 异步图片加载

    /// 异步加载图片
    /// - Parameters:
    ///   - data: 图片数据
    ///   - key: 缓存键
    ///   - useProgressiveJPEG: 是否使用渐进式JPEG
    ///   - completion: 完成回调
    func loadImageAsync(data: Data, key: String, useProgressiveJPEG: Bool = true, completion: @escaping (UIImage?) -> Void) {
        // 检查缓存
        if let cachedImage = getImageFromCache(key: key) {
            completion(cachedImage)
            return
        }

        // 后台线程加载图片
        DispatchQueue.global(qos: .userInitiated).async {
            autoreleasepool {
                guard let image = UIImage(data: data) else {
                    DispatchQueue.main.async {
                        completion(nil)
                    }
                    return
                }

                // 压缩图片
                let compressedImage = self.compressImage(image)

                // 缓存图片
                self.cacheImage(compressedImage, key: key)

                // 生成并缓存缩略图
                let thumbnail = self.generateThumbnail(image)
                self.cacheThumbnail(thumbnail, key: key + "_thumb")

                // 主线程返回结果
                DispatchQueue.main.async {
                    // 标记为已完全加载
                    self.markAsFullyLoaded(key: key)
                    completion(compressedImage)
                }

                // 优化：减少渐进式JPEG转换，只在真正需要时进行
                // 移除自动转换逻辑，减少不必要的处理和内存占用
            }
        }
    }

    /// 异步加载缩略图
    /// - Parameters:
    ///   - data: 图片数据
    ///   - key: 缓存键
    ///   - completion: 完成回调
    func loadThumbnailAsync(data: Data, key: String, completion: @escaping (UIImage?) -> Void) {
        // 检查缓存
        if let cachedThumbnail = getThumbnailFromCache(key: key + "_thumb") {
            completion(cachedThumbnail)
            return
        }

        // 后台线程加载图片
        DispatchQueue.global(qos: .userInitiated).async {
            autoreleasepool {
                guard let image = UIImage(data: data) else {
                    DispatchQueue.main.async {
                        completion(nil)
                    }
                    return
                }

                // 生成缩略图
                let thumbnail = self.generateThumbnail(image)

                // 缓存缩略图
                self.cacheThumbnail(thumbnail, key: key + "_thumb")

                // 主线程返回结果
                DispatchQueue.main.async {
                    completion(thumbnail)
                }
            }
        }
    }

    /// 异步加载渐进式JPEG图片
    /// - Parameters:
    ///   - data: 图片数据
    ///   - key: 缓存键
    ///   - progressHandler: 进度回调，返回不同质量级别的图片
    ///   - completion: 完成回调
    func loadProgressiveJPEGAsync(data: Data, key: String, progressHandler: @escaping (UIImage, CGFloat) -> Void, completion: @escaping (UIImage?) -> Void) {
        // 检查缓存
        if let cachedImage = getImageFromCache(key: key) {
            completion(cachedImage)
            return
        }

        // 检查是否为渐进式JPEG
        let isProgressive = isProgressiveJPEG(data)

        // 如果不是渐进式JPEG，使用普通加载方法
        if !isProgressive {
            loadImageAsync(data: data, key: key, useProgressiveJPEG: true) { image in
                completion(image)
            }
            return
        }

        // 后台线程加载渐进式JPEG
        DispatchQueue.global(qos: .userInitiated).async {
            autoreleasepool {
                // 创建图像源
                guard let source = CGImageSourceCreateWithData(data as CFData, nil) else {
                    DispatchQueue.main.async {
                        completion(nil)
                    }
                    return
                }

                // 设置增量加载选项
                let options: [CFString: Any] = [
                    kCGImageSourceCreateThumbnailFromImageAlways: true,
                    kCGImageSourceCreateThumbnailWithTransform: true,
                    kCGImageSourceShouldCacheImmediately: false
                ]

                // 优化：只使用5个关键质量级别，大幅减少内存占用
                // 保持渐进式加载体验但避免生成过多副本
                let qualityLevels: [CGFloat] = [0.1, 0.3, 0.6, 0.8, 1.0]

                // 创建一个操作队列，用于顺序处理不同质量级别
                let queue = DispatchQueue(label: "com.pinklog.progressiveJPEG", qos: .userInteractive)

                // 在后台队列中处理图片
                queue.async {
                    var lastQualityReported: CGFloat = 0
                    var lastImageReported: UIImage? = nil

                    for (index, quality) in qualityLevels.enumerated() {
                        // 简化：不再跳过质量级别，因为只有5个级别

                        // 创建缩略图选项
                        var thumbOptions = options
                        thumbOptions[kCGImageSourceThumbnailMaxPixelSize] = Int(1024 * quality)

                        // 创建缩略图
                        if let cgImage = CGImageSourceCreateThumbnailAtIndex(source, 0, thumbOptions as CFDictionary) {
                            let image = UIImage(cgImage: cgImage)

                            // 检查图片是否与上一张相同（避免重复报告相同的图片）
                            let isSameAsLast = lastImageReported != nil &&
                                               image.size.width == lastImageReported!.size.width &&
                                               image.size.height == lastImageReported!.size.height

                            if !isSameAsLast || index == qualityLevels.count - 1 {
                                // 主线程返回进度
                                DispatchQueue.main.async {
                                    progressHandler(image, quality)
                                    lastQualityReported = quality
                                    lastImageReported = image

                                    // 最后一个质量级别，完成加载
                                    if index == qualityLevels.count - 1 {
                                        // 缓存最终图片
                                        self.cacheImage(image, key: key)
                                        // 标记为已完全加载
                                        self.markAsFullyLoaded(key: key)
                                        completion(image)
                                    }
                                }
                            }

                            // 简化延迟逻辑：只在级别间添加固定的短暂延迟
                            if index < qualityLevels.count - 1 {
                                Thread.sleep(forTimeInterval: 0.05) // 固定50ms延迟
                            }
                        }
                    }
                }
            }
        }
    }
}
