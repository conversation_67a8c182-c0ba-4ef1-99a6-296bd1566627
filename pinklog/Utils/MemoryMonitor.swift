import Foundation
import UIKit
import Darwin

/// 内存监控工具类，用于监控应用的内存使用情况
class MemoryMonitor {
    static let shared = MemoryMonitor()
    
    private var memoryWarningThreshold: Double = 500.0 // MB
    private var criticalMemoryThreshold: Double = 800.0 // MB
    private var memoryReadings: [MemoryReading] = []
    private var maxReadings = 100 // 最多保存100个读数
    
    private init() {
        setupMemoryWarningNotification()
    }
    
    // MARK: - Memory Reading Structure
    
    struct MemoryReading {
        let timestamp: Date
        let memoryUsage: Double // MB
        let context: String
        let isWarning: Bool
        let isCritical: Bool
    }
    
    // MARK: - Memory Monitoring
    
    /// 获取当前内存使用量（MB）
    func getCurrentMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / 1024.0 / 1024.0
        } else {
            return 0.0
        }
    }
    
    /// 记录内存使用情况
    /// - Parameter context: 上下文描述
    func recordMemoryUsage(_ context: String) {
        let currentUsage = getCurrentMemoryUsage()
        let isWarning = currentUsage > memoryWarningThreshold
        let isCritical = currentUsage > criticalMemoryThreshold
        
        let reading = MemoryReading(
            timestamp: Date(),
            memoryUsage: currentUsage,
            context: context,
            isWarning: isWarning,
            isCritical: isCritical
        )
        
        memoryReadings.append(reading)
        
        // 保持读数数量在限制内
        if memoryReadings.count > maxReadings {
            memoryReadings.removeFirst()
        }
        
        // 输出日志
        #if DEBUG
        let status = isCritical ? "🔴 CRITICAL" : (isWarning ? "🟡 WARNING" : "🟢 NORMAL")
        print("[\(status)] \(context): \(String(format: "%.1f", currentUsage)) MB")
        #endif
        
        // 如果内存使用过高，触发清理
        if isCritical {
            triggerMemoryCleanup()
        }
    }
    
    /// 获取内存使用统计信息
    func getMemoryStatistics() -> MemoryStatistics {
        let currentUsage = getCurrentMemoryUsage()
        let recentReadings = Array(memoryReadings.suffix(10))
        
        let averageUsage = recentReadings.isEmpty ? currentUsage : 
            recentReadings.map { $0.memoryUsage }.reduce(0, +) / Double(recentReadings.count)
        
        let maxUsage = memoryReadings.map { $0.memoryUsage }.max() ?? currentUsage
        let minUsage = memoryReadings.map { $0.memoryUsage }.min() ?? currentUsage
        
        let warningCount = memoryReadings.filter { $0.isWarning }.count
        let criticalCount = memoryReadings.filter { $0.isCritical }.count
        
        return MemoryStatistics(
            currentUsage: currentUsage,
            averageUsage: averageUsage,
            maxUsage: maxUsage,
            minUsage: minUsage,
            warningCount: warningCount,
            criticalCount: criticalCount,
            totalReadings: memoryReadings.count
        )
    }
    
    // MARK: - Memory Statistics Structure
    
    struct MemoryStatistics {
        let currentUsage: Double
        let averageUsage: Double
        let maxUsage: Double
        let minUsage: Double
        let warningCount: Int
        let criticalCount: Int
        let totalReadings: Int
        
        var description: String {
            return """
            内存使用统计:
            - 当前使用: \(String(format: "%.1f", currentUsage)) MB
            - 平均使用: \(String(format: "%.1f", averageUsage)) MB
            - 最大使用: \(String(format: "%.1f", maxUsage)) MB
            - 最小使用: \(String(format: "%.1f", minUsage)) MB
            - 警告次数: \(warningCount)
            - 严重警告次数: \(criticalCount)
            - 总记录数: \(totalReadings)
            """
        }
    }
    
    // MARK: - Memory Warning Handling
    
    private func setupMemoryWarningNotification() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    @objc private func handleMemoryWarning() {
        recordMemoryUsage("系统内存警告")
        triggerMemoryCleanup()
    }
    
    private func triggerMemoryCleanup() {
        #if DEBUG
        print("🧹 触发内存清理...")
        #endif

        // 清理ImageManager缓存
        ImageManager.shared.clearTemporaryCache()

        // 强制垃圾回收
        autoreleasepool {
            // 触发内存清理
        }

        // 记录清理后的内存使用（不触发新的清理）
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let currentUsage = self.getCurrentMemoryUsage()
            let reading = MemoryReading(
                timestamp: Date(),
                memoryUsage: currentUsage,
                context: "内存清理后",
                isWarning: false, // 强制设为false，避免循环
                isCritical: false // 强制设为false，避免循环
            )

            self.memoryReadings.append(reading)

            // 保持读数数量在限制内
            if self.memoryReadings.count > self.maxReadings {
                self.memoryReadings.removeFirst()
            }

            #if DEBUG
            print("[🟢 NORMAL] 内存清理后: \(String(format: "%.1f", currentUsage)) MB")
            #endif
        }
    }
    
    // MARK: - Configuration
    
    /// 设置内存警告阈值
    /// - Parameters:
    ///   - warning: 警告阈值（MB）
    ///   - critical: 严重警告阈值（MB）
    func setMemoryThresholds(warning: Double, critical: Double) {
        memoryWarningThreshold = warning
        criticalMemoryThreshold = critical
    }
    
    /// 清除所有内存读数
    func clearReadings() {
        memoryReadings.removeAll()
    }
    
    /// 导出内存读数（用于调试）
    func exportReadings() -> [MemoryReading] {
        return memoryReadings
    }
    
    // MARK: - Convenience Methods
    
    /// 监控代码块的内存使用
    /// - Parameters:
    ///   - context: 上下文描述
    ///   - block: 要监控的代码块
    func monitorMemoryUsage<T>(_ context: String, block: () throws -> T) rethrows -> T {
        recordMemoryUsage("\(context) - 开始")
        let result = try block()
        recordMemoryUsage("\(context) - 结束")
        return result
    }
    
    /// 异步监控代码块的内存使用
    /// - Parameters:
    ///   - context: 上下文描述
    ///   - block: 要监控的异步代码块
    func monitorMemoryUsageAsync<T>(_ context: String, block: @escaping () async throws -> T) async rethrows -> T {
        recordMemoryUsage("\(context) - 开始")
        let result = try await block()
        recordMemoryUsage("\(context) - 结束")
        return result
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
