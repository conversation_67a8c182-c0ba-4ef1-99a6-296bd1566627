//
//  SubjectLiftManager.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import UIKit
import Vision
import CoreImage

class SubjectLiftManager {
    static let shared = SubjectLiftManager()

    private init() {}

    /// 从图片中抠出主体
    /// - Parameters:
    ///   - image: 原始图片
    ///   - completion: 完成回调，返回抠图结果或错误
    func liftSubject(from image: UIImage, completion: @escaping (Result<UIImage, SubjectLiftError>) -> Void) {
        guard let ciImage = CIImage(image: image) else {
            completion(.failure(.invalidImage))
            return
        }

        DispatchQueue.global(qos: .userInitiated).async {
            if #available(iOS 16.0, *) {
                self.liftSubjectIOS16(from: ciImage, originalImage: image, completion: completion)
            } else {
                self.liftSubjectLegacy(from: ciImage, originalImage: image, completion: completion)
            }
        }
    }

    /// 自动处理：抠图 + 贴纸化
    /// - Parameters:
    ///   - image: 原始图片
    ///   - completion: 完成回调，返回(抠图结果, 贴纸结果)或错误
    func autoProcessImage(from image: UIImage, completion: @escaping (Result<(liftedImage: UIImage, stickerImage: UIImage), SubjectLiftError>) -> Void) {
        // 🔥 使用更激进的内存管理策略
        DispatchQueue.global(qos: .userInitiated).async {
            autoreleasepool {
                // 记录内存使用情况
                self.logMemoryUsage("SubjectLiftManager开始自动处理")

                // 🔥 关键修复：立即压缩原图到合理尺寸，避免后续处理占用大量内存
                let processedImage = ImageManager.shared.compressImage(image, maxSize: 1024, quality: 0.8)
                self.logMemoryUsage("原图压缩完成")

                // 🔥 立即清理原图引用，释放大图片内存
                // 注意：这里不能直接修改传入的image参数，但可以确保不再引用它

                // 首先进行抠图（使用压缩后的图片）
                self.liftSubject(from: processedImage) { result in
                    autoreleasepool {
                        switch result {
                        case .success(let liftedImage):
                            self.logMemoryUsage("抠图完成，开始贴纸化")

                            // 抠图成功，进行贴纸化
                            DispatchQueue.global(qos: .userInitiated).async {
                                autoreleasepool {
                                    if let stickerImage = StickerStyleProcessor.shared.createStickerStyle(from: liftedImage) {
                                        DispatchQueue.main.async {
                                            self.logMemoryUsage("贴纸化完成")
                                            completion(.success((liftedImage: liftedImage, stickerImage: stickerImage)))
                                        }
                                    } else {
                                        // 贴纸化失败，但抠图成功，返回抠图结果作为贴纸
                                        DispatchQueue.main.async {
                                            self.logMemoryUsage("贴纸化失败，使用抠图作为贴纸")
                                            completion(.success((liftedImage: liftedImage, stickerImage: liftedImage)))
                                        }
                                    }
                                }
                            }
                        case .failure(let error):
                            self.logMemoryUsage("抠图失败")
                            completion(.failure(error))
                        }
                    }
                }
            }
        }
    }

    @available(iOS 16.0, *)
    private func liftSubjectIOS16(from ciImage: CIImage, originalImage: UIImage, completion: @escaping (Result<UIImage, SubjectLiftError>) -> Void) {
        let request = VNGenerateForegroundInstanceMaskRequest()
        let handler = VNImageRequestHandler(ciImage: ciImage, options: [:])
        
        do {
            try handler.perform([request])
            
            guard let result = request.results?.first else {
                DispatchQueue.main.async {
                    completion(.failure(.noSubjectDetected))
                }
                return
            }
            
            // 生成蒙版
            let maskPixelBuffer = try result.generateScaledMaskForImage(
                forInstances: result.allInstances,
                from: handler
            )
            
            let maskImage = CIImage(cvPixelBuffer: maskPixelBuffer)
            
            // 应用蒙版创建抠图效果
            guard let filter = CIFilter(name: "CIBlendWithMask") else {
                DispatchQueue.main.async {
                    completion(.failure(.processingFailed))
                }
                return
            }

            filter.setValue(ciImage, forKey: kCIInputImageKey)
            filter.setValue(maskImage, forKey: kCIInputMaskImageKey)
            filter.setValue(CIImage.empty(), forKey: kCIInputBackgroundImageKey)

            guard let outputImage = filter.outputImage else {
                DispatchQueue.main.async {
                    completion(.failure(.processingFailed))
                }
                return
            }
            
            // 渲染为UIImage，保持原始图片的方向
            autoreleasepool {
                let context = CIContext()
                guard let cgImage = context.createCGImage(outputImage, from: outputImage.extent) else {
                    DispatchQueue.main.async {
                        completion(.failure(.renderingFailed))
                    }
                    return
                }

                // 创建UIImage时保持原始图片的方向信息
                let resultImage = UIImage(cgImage: cgImage, scale: originalImage.scale, orientation: originalImage.imageOrientation)

                DispatchQueue.main.async {
                    completion(.success(resultImage))
                }
            }
            
        } catch {
            DispatchQueue.main.async {
                completion(.failure(.visionRequestFailed(error)))
            }
        }
    }
    
    private func liftSubjectLegacy(from ciImage: CIImage, originalImage: UIImage, completion: @escaping (Result<UIImage, SubjectLiftError>) -> Void) {
        let request = VNGeneratePersonSegmentationRequest { request, error in
            guard error == nil,
                  let results = request.results as? [VNPixelBufferObservation],
                  let observation = results.first else {
                DispatchQueue.main.async {
                    completion(.failure(.legacyVersionNotSupported))
                }
                return
            }
            
            // 处理人像分割结果
            autoreleasepool {
                let maskImage = CIImage(cvPixelBuffer: observation.pixelBuffer)
                let subjectImage = self.applyMatte(originalCIImage: ciImage, matteCIImage: maskImage)

                let context = CIContext()
                guard let cgImage = context.createCGImage(subjectImage, from: subjectImage.extent) else {
                    DispatchQueue.main.async {
                        completion(.failure(.renderingFailed))
                    }
                    return
                }

                let finalUIImage = UIImage(cgImage: cgImage, scale: originalImage.scale, orientation: originalImage.imageOrientation)

                DispatchQueue.main.async {
                    completion(.success(finalUIImage))
                }
            }
        }
        
        let handler = VNImageRequestHandler(ciImage: ciImage)
        do {
            try handler.perform([request])
        } catch {
            DispatchQueue.main.async {
                completion(.failure(.visionRequestFailed(error)))
            }
        }
    }
    
    private func applyMatte(originalCIImage: CIImage, matteCIImage: CIImage) -> CIImage {
        guard let filter = CIFilter(name: "CIBlendWithMask") else {
            return originalCIImage
        }

        filter.setValue(originalCIImage, forKey: kCIInputImageKey)
        filter.setValue(matteCIImage, forKey: kCIInputMaskImageKey)
        filter.setValue(CIImage.empty(), forKey: kCIInputBackgroundImageKey)

        return filter.outputImage ?? originalCIImage
    }

    /// 监控内存使用情况（调试用）
    private func logMemoryUsage(_ context: String) {
        MemoryMonitor.shared.recordMemoryUsage("SubjectLiftManager: \(context)")
    }
}

// MARK: - 错误类型定义
enum SubjectLiftError: LocalizedError {
    case invalidImage
    case noSubjectDetected
    case processingFailed
    case renderingFailed
    case visionRequestFailed(Error)
    case legacyVersionNotSupported
    
    var errorDescription: String? {
        switch self {
        case .invalidImage:
            return "无法处理图片数据"
        case .noSubjectDetected:
            return "未检测到主体对象，请尝试其他图片或使用原图"
        case .processingFailed:
            return "抠图处理失败，请重试"
        case .renderingFailed:
            return "图像渲染失败"
        case .visionRequestFailed(let error):
            return "抠图失败: \(error.localizedDescription)"
        case .legacyVersionNotSupported:
            return "该功能需要iOS 16或更高版本以获得最佳抠图效果。\n您可以选择使用原图创建物品。"
        }
    }
}
