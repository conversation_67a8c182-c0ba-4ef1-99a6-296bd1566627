import SwiftUI

/// 异步加载图片的视图组件
struct AsyncImageView: View {
    // MARK: - 属性

    /// 图片数据
    let imageData: Data?

    /// 图片缓存键
    let cacheKey: String

    /// 是否加载缩略图
    var loadThumbnail: Bool = true



    /// 内容模式
    var contentMode: ContentMode = .fill

    /// 占位图
    var placeholder: AnyView = AnyView(
        Rectangle()
            .fill(Color.gray.opacity(0.2))
            .overlay(
                Image(systemName: "photo")
                    .font(.title)
                    .foregroundColor(.gray)
            )
    )

    // MARK: - 状态变量

    /// 当前图片
    @State private var currentImage: UIImage? = nil

    /// 上一个图片（用于交叉淡入淡出）
    @State private var previousImage: UIImage? = nil

    /// 是否正在加载
    @State private var isLoading = true

    /// 加载质量 (0.0-1.0)
    @State private var loadingQuality: CGFloat = 0.0

    /// 上一个加载质量
    @State private var previousQuality: CGFloat = 0.0

    /// 交叉淡入淡出进度 (0.0-1.0)
    @State private var crossfadeProgress: CGFloat = 1.0

    /// 是否是首次加载
    @State private var isFirstLoad = true

    // MARK: - 初始化

    /// 使用图片数据初始化
    /// - Parameters:
    ///   - imageData: 图片数据
    ///   - cacheKey: 缓存键
    ///   - loadThumbnail: 是否加载缩略图
    ///   - contentMode: 内容模式
    init(imageData: Data?, cacheKey: String, loadThumbnail: Bool = true, contentMode: ContentMode = .fill) {
        self.imageData = imageData
        self.cacheKey = cacheKey
        self.loadThumbnail = loadThumbnail
        self.contentMode = contentMode
    }

    /// 使用自定义占位图初始化
    /// - Parameters:
    ///   - imageData: 图片数据
    ///   - cacheKey: 缓存键
    ///   - loadThumbnail: 是否加载缩略图
    ///   - contentMode: 内容模式
    ///   - placeholder: 占位图
    init<P: View>(imageData: Data?, cacheKey: String, loadThumbnail: Bool = true, contentMode: ContentMode = .fill, @ViewBuilder placeholder: () -> P) {
        self.imageData = imageData
        self.cacheKey = cacheKey
        self.loadThumbnail = loadThumbnail
        self.contentMode = contentMode
        self.placeholder = AnyView(placeholder())
    }

    // MARK: - 视图

    var body: some View {
        ZStack {
            // 占位图 - 只在没有任何图片时显示
            if isLoading && currentImage == nil && previousImage == nil {
                placeholder
            }

            // 图片容器 - 包含当前图片和上一个图片，实现简单的交叉淡入淡出
            ZStack {
                // 上一个图片
                if let previousImage = previousImage {
                    Image(uiImage: previousImage)
                        .resizable()
                        .aspectRatio(contentMode: contentMode)
                        .opacity(1 - crossfadeProgress)
                }

                // 当前图片
                if let currentImage = currentImage {
                    Image(uiImage: currentImage)
                        .resizable()
                        .aspectRatio(contentMode: contentMode)
                        .opacity(crossfadeProgress)
                }
            }
            // 只应用非常微妙的亮度变化，移除其他视觉效果
            .brightness(calculateBrightness())
            // 确保图片容器始终填满父视图
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        .onAppear {
            loadImage()
        }
        .animation(.none) // 禁用隐式动画，使用显式动画控制过渡
    }

    // MARK: - 辅助方法

    /// 计算模糊半径 - 移除模糊效果
    private func calculateBlurRadius() -> CGFloat {
        // 完全移除模糊效果，避免反复模糊到清晰的过程
        return 0
    }

    /// 计算亮度 - 使用非常微妙的亮度变化
    private func calculateBrightness() -> Double {
        return 0 // 不再使用渐进式JPEG，返回默认亮度
    }

    /// 计算缩放比例 - 移除缩放效果
    private func calculateScale() -> CGFloat {
        // 完全移除缩放效果，避免反复缩放
        return 1.0
    }

    // MARK: - 方法

    /// 加载图片
    private func loadImage() {
        // 如果没有图片数据，直接返回
        guard let imageData = imageData else {
            isLoading = false
            return
        }

        // 首先检查是否已有高质量缓存
        if ImageManager.shared.hasHighQualityCache(key: cacheKey) {
            // 直接使用高质量缓存
            if let cachedImage = ImageManager.shared.getImageFromCache(key: cacheKey) {
                // 直接设置为高质量图片，不需要渐进式加载
                withAnimation(.easeInOut(duration: 0.3)) {
                    self.currentImage = cachedImage
                    self.isLoading = false
                    self.loadingQuality = 1.0
                    self.isFirstLoad = false
                }
                return
            }
        }

        // 直接使用普通图片加载

        // 如果需要加载缩略图
        if loadThumbnail {
            // 尝试从缓存中获取缩略图
            if let cachedThumbnail = ImageManager.shared.getThumbnailFromCache(key: cacheKey + "_thumb") {
                updateImage(cachedThumbnail, quality: 0.5)

                // 异步加载高清图片
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    loadFullImage(imageData: imageData)
                }
                return
            }

            // 异步加载缩略图
            ImageManager.shared.loadThumbnailAsync(data: imageData, key: cacheKey) { thumbnail in
                if let thumbnail = thumbnail {
                    updateImage(thumbnail, quality: 0.5)

                    // 异步加载高清图片，但稍微延迟以确保缩略图动画完成
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        loadFullImage(imageData: imageData)
                    }
                }
            }
        } else {
            // 直接加载高清图片
            loadFullImage(imageData: imageData)
        }
    }

    /// 加载高清图片
    /// - Parameter imageData: 图片数据
    private func loadFullImage(imageData: Data) {
        // 首先检查是否已有高质量缓存
        if ImageManager.shared.hasHighQualityCache(key: cacheKey) {
            // 直接使用高质量缓存
            if let cachedImage = ImageManager.shared.getImageFromCache(key: cacheKey) {
                // 直接设置为高质量图片，不需要渐进式加载
                withAnimation(.easeInOut(duration: 0.3)) {
                    self.currentImage = cachedImage
                    self.isLoading = false
                    self.loadingQuality = 1.0
                    self.isFirstLoad = false
                }
                return
            }
        }

        // 尝试从缓存中获取高清图片（可能未标记为完全加载）
        if let cachedImage = ImageManager.shared.getImageFromCache(key: cacheKey) {
            updateImage(cachedImage, quality: 1.0)
            // 标记为已完全加载
            ImageManager.shared.markAsFullyLoaded(key: cacheKey)
            return
        }

        // 异步加载高清图片
        ImageManager.shared.loadImageAsync(data: imageData, key: cacheKey) { image in
            if let image = image {
                // 使用两阶段过渡，先到0.8，然后到1.0
                updateImage(image, quality: 0.8)

                // 直接设置为最终值，不再延迟
                DispatchQueue.main.async {
                    // 使用更平滑的过渡到最终质量
                    withAnimation(.easeOut(duration: 0.5)) {
                        self.previousQuality = self.loadingQuality
                        self.loadingQuality = 1.0
                    }
                }
            }
        }
    }



    /// 更新图片，实现平滑过渡
    /// - Parameters:
    ///   - newImage: 新图片
    ///   - quality: 图片质量
    private func updateImage(_ newImage: UIImage, quality: CGFloat) {
        // 如果是首次加载，使用简单的淡入效果
        if isFirstLoad {
            withAnimation(.easeInOut(duration: 0.5)) {
                self.currentImage = newImage
                self.isLoading = false
                self.loadingQuality = quality // 直接设置为最终质量
                self.isFirstLoad = false
            }
            return
        }

        // 保存当前状态
        self.previousImage = self.currentImage
        self.previousQuality = self.loadingQuality

        // 设置新图片
        self.currentImage = newImage

        // 重置交叉淡入淡出进度
        self.crossfadeProgress = 0.0

        // 直接设置为最终质量，避免中间状态
        self.loadingQuality = quality

        // 动画交叉淡入淡出 - 使用较长的动画时间
        withAnimation(.easeInOut(duration: 0.8)) {
            self.crossfadeProgress = 1.0
        }

        // 动画完成后清除上一个图片，释放内存
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.previousImage = nil
        }
    }
}

/// 异步加载图片的视图组件（支持点击预览）
struct AsyncImageViewWithPreview: View {
    // MARK: - 属性

    /// 图片数据
    let imageData: Data?

    /// 图片缓存键
    let cacheKey: String

    /// 是否加载缩略图
    var loadThumbnail: Bool = true



    /// 内容模式
    var contentMode: ContentMode = .fill

    /// 是否显示预览
    @State private var showingPreview = false

    // MARK: - 视图

    var body: some View {
        AsyncImageView(
            imageData: imageData,
            cacheKey: cacheKey,
            loadThumbnail: loadThumbnail,
            contentMode: contentMode
        )
        .onTapGesture {
            showingPreview = true
        }
        .fullScreenCover(isPresented: $showingPreview) {
            // 图片预览视图
            ZStack {
                // 黑色背景
                Color.black.ignoresSafeArea()

                // 使用预览专用的缓存键，确保预览图片单独加载和缓存
                AsyncImageView(
                    imageData: imageData,
                    cacheKey: cacheKey + "_preview",
                    loadThumbnail: false, // 预览时不使用缩略图，直接加载高清图片
                    contentMode: .fit
                )

                // 关闭按钮
                VStack {
                    HStack {
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                showingPreview = false
                            }
                        }) {
                            Image(systemName: "xmark")
                                .font(.title2)
                                .foregroundColor(.white)
                                .padding(12)
                                .background(Circle().fill(Color.black.opacity(0.5)))
                        }
                        .padding()

                        Spacer()
                    }
                    Spacer()
                }
            }
            .statusBar(hidden: true)
            .transition(.opacity) // 添加过渡效果
        }
    }
}
