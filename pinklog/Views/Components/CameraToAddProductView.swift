//
//  CameraToAddProductView.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import SwiftUI
import AVFoundation

struct CameraToAddProductView: View {
    var onProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)?
    @Environment(\.presentationMode) var presentationMode

    @State private var showingForm = false
    @State private var capturedImages: (original: UIImage, lifted: UIImage, sticker: UIImage)?
    @State private var isProcessing = false
    @State private var processingProgress: Double = 0.0
    @State private var capturedImagePreview: UIImage?

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 相机界面 - 始终存在，通过变形过渡到表单
                if !showingForm {
                    cameraView
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .scaleEffect(showingForm ? 0.8 : 1.0)
                        .opacity(showingForm ? 0 : 1)
                }

                // 表单界面 - 从相机界面变形而来
                if showingForm, let images = capturedImages {
                    formView(images: images)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .scaleEffect(showingForm ? 1.0 : 0.8)
                        .opacity(showingForm ? 1 : 0)
                }

                // 处理状态覆盖层 - 在相机界面上显示
                if isProcessing {
                    processingOverlay
                }
            }
        }
        .ignoresSafeArea()
        .animation(.spring(response: 0.8, dampingFraction: 0.85, blendDuration: 0.2), value: showingForm)
        .animation(.easeInOut(duration: 0.3), value: isProcessing)
    }

    // MARK: - 相机视图
    private var cameraView: some View {
        CameraViewControllerWrapper { originalImage, liftedImg, stickerImg in
            autoreleasepool {
                // 记录内存使用情况
                logMemoryUsage("拍照完成，处理前")

                capturedImagePreview = originalImage
                capturedImages = (original: originalImage, lifted: liftedImg, sticker: stickerImg)
                onProcessCompleted?(originalImage, liftedImg, stickerImg)

                // 开始处理状态
                isProcessing = true
                startProcessingAnimation()

                // 记录内存使用情况
                logMemoryUsage("拍照完成，处理后")
            }
        }
        .overlay(
            // 相机控制栏
            VStack {
                HStack {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(.white)
                    .font(.system(size: 17, weight: .medium))
                    .padding()

                    Spacer()
                }

                Spacer()
            }
        )
    }

    // MARK: - 处理状态覆盖层
    private var processingOverlay: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.75)
                .ignoresSafeArea()

            VStack(spacing: 24) {
                // 预览图片 - 带微妙动画
                if let preview = capturedImagePreview {
                    Image(uiImage: preview)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 120, height: 120)
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.white.opacity(0.4), lineWidth: 2)
                        )
                        .scaleEffect(1.0 + sin(processingProgress * .pi * 4) * 0.03)
                }

                // 处理进度
                VStack(spacing: 12) {
                    Text("正在创建贴纸风格...")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)

                    // 简洁的进度条
                    ProgressView(value: processingProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .white))
                        .frame(width: 180)
                        .scaleEffect(y: 2)

                    Text("\(Int(processingProgress * 100))%")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                }
            }
        }
    }

    // MARK: - 表单视图
    private func formView(images: (original: UIImage, lifted: UIImage, sticker: UIImage)) -> some View {
        NavigationView {
            AddProductView(
                presetImage: images.original,
                presetLiftedImage: images.lifted,
                presetStickerImage: images.sticker,
                showCancelButton: false,
                onSaveCompleted: {
                    print("🎉 保存完成，关闭整个拍照添加流程")
                    presentationMode.wrappedValue.dismiss()
                }
            )
            .navigationBarItems(leading: Button("取消") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }

    // MARK: - 处理动画
    private func startProcessingAnimation() {
        processingProgress = 0.0

        // 快速但平滑的进度动画
        Timer.scheduledTimer(withTimeInterval: 0.03, repeats: true) { timer in
            processingProgress += 0.04

            if processingProgress >= 1.0 {
                timer.invalidate()
                processingProgress = 1.0

                // 短暂延迟后直接变形到表单
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
                    isProcessing = false

                    // 优雅的变形过渡到表单
                    withAnimation(.spring(response: 0.8, dampingFraction: 0.85, blendDuration: 0.2)) {
                        showingForm = true
                    }

                    // 清理预览图片，释放内存
                    capturedImagePreview = nil
                    logMemoryUsage("动画完成，清理预览图片后")
                }
            }
        }
    }

    // MARK: - Helper Methods

    /// 监控内存使用情况（调试用）
    private func logMemoryUsage(_ context: String) {
        MemoryMonitor.shared.recordMemoryUsage("CameraToAddProductView: \(context)")
    }
}

// MARK: - 不自动关闭的相机控制器
class NonDismissingCameraController: CameraController {
    override func startAutoProcessing(originalImage: UIImage, callback: @escaping (UIImage, UIImage, UIImage) -> Void) {
        autoreleasepool {
            // 记录内存使用情况
            logMemoryUsage("开始自动处理")

            // 显示处理指示器
            showProcessingIndicator()

            SubjectLiftManager.shared.autoProcessImage(from: originalImage) { result in
                autoreleasepool {
                    DispatchQueue.main.async {
                        self.hideProcessingIndicator()

                        switch result {
                        case .success(let (liftedImage, stickerImage)):
                            callback(originalImage, liftedImage, stickerImage)
                            // 不自动dismiss，让父视图控制
                        case .failure(_):
                            // 处理失败，返回原图
                            callback(originalImage, originalImage, originalImage)
                            // 不自动dismiss，让父视图控制
                        }

                        // 记录内存使用情况
                        self.logMemoryUsage("自动处理完成")
                    }
                }
            }
        }
    }

    /// 监控内存使用情况（调试用）
    private func logMemoryUsage(_ context: String) {
        MemoryMonitor.shared.recordMemoryUsage("NonDismissingCameraController: \(context)")
    }
}

// MARK: - 相机控制器包装器
struct CameraViewControllerWrapper: UIViewControllerRepresentable {
    var onProcessCompleted: ((UIImage, UIImage, UIImage) -> Void)?
    @Environment(\.presentationMode) var presentationMode

    func makeUIViewController(context: Context) -> NonDismissingCameraController {
        let controller = NonDismissingCameraController()
        controller.onAutoProcessCompleted = onProcessCompleted
        return controller
    }

    func updateUIViewController(_ uiViewController: NonDismissingCameraController, context: Context) {}
}

#Preview {
    CameraToAddProductView { _, _, _ in
        // Preview callback
    }
}
