import SwiftUI

/// 图片集合预览组件
struct ImageGalleryView: View {
    // MARK: - 属性

    /// 图片数据数组
    let imageDatas: [Data]

    /// 基础缓存键
    let baseCacheKey: String



    /// 是否显示预览
    @State private var showingPreview = false

    /// 预览的图片索引
    @State private var previewIndex = 0

    // MARK: - 视图

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "photo")
                    .foregroundColor(.blue)
                Text("照片记忆")
                    .font(.headline)
                Spacer()
            }

            // 图片横向滚动视图
            ScrollView(.horizontal, showsIndicators: false) {
                LazyHStack(spacing: 12) {
                    ForEach(0..<imageDatas.count, id: \.self) { index in
                        // 使用异步图片视图加载缩略图
                        AsyncImageView(
                            imageData: imageDatas[index],
                            cacheKey: "\(baseCacheKey)_\(index)",
                            loadThumbnail: true,
                            contentMode: .fill
                        )
                        .frame(width: 160, height: 200)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                        .onTapGesture {
                            previewIndex = index
                            showingPreview = true
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        .fullScreenCover(isPresented: $showingPreview) {
            // 全屏图片预览
            ImageGalleryPreview(
                imageDatas: imageDatas,
                baseCacheKey: baseCacheKey,
                initialIndex: previewIndex,
                onDismiss: { showingPreview = false }
            )
        }
    }
}

/// 图片集合全屏预览
struct ImageGalleryPreview: View {
    // MARK: - 属性

    /// 图片数据数组
    let imageDatas: [Data]

    /// 基础缓存键
    let baseCacheKey: String

    /// 初始索引
    let initialIndex: Int



    /// 关闭回调
    let onDismiss: () -> Void

    /// 当前索引
    @State private var currentIndex: Int

    /// 加载状态
    @State private var loadingStates: [Bool]

    // MARK: - 初始化

    init(imageDatas: [Data], baseCacheKey: String, initialIndex: Int, onDismiss: @escaping () -> Void) {
        self.imageDatas = imageDatas
        self.baseCacheKey = baseCacheKey
        self.initialIndex = initialIndex
        self.onDismiss = onDismiss
        self._currentIndex = State(initialValue: initialIndex)
        self._loadingStates = State(initialValue: Array(repeating: false, count: imageDatas.count))
    }

    // MARK: - 视图

    var body: some View {
        // 使用NavigationView确保系统手势能够正确传递
        NavigationView {
            // 使用TabView实现滑动预览
            TabView(selection: $currentIndex) {
                ForEach(0..<imageDatas.count, id: \.self) { index in
                    ZStack {
                        // 使用异步图片视图加载高清图片
                        AsyncImageView(
                            imageData: imageDatas[index],
                            cacheKey: "\(baseCacheKey)_preview_\(index)",
                            loadThumbnail: false,
                            contentMode: .fit
                        )
                        .tag(index)
                        .onAppear {
                            // 标记当前图片为正在加载
                            loadingStates[index] = true

                            // 预加载相邻图片
                            preloadAdjacentImages()
                        }

                        // 加载指示器
                        if !loadingStates[index] {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(1.5)
                        }
                    }
                }
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            .background(Color.black)
            .onAppear {
                // 优化TabView的滑动性能
                UIScrollView.appearance().decelerationRate = .fast

                // 预加载相邻图片
                preloadAdjacentImages()
            }
            .onChange(of: currentIndex) { _ in
                // 当索引变化时预加载相邻图片
                preloadAdjacentImages()
            }
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button(action: onDismiss) {
                    Image(systemName: "xmark")
                        .foregroundColor(.white)
                },
                trailing: Text("\(currentIndex + 1) / \(imageDatas.count)")
                    .foregroundColor(.white)
            )
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text("")
                }
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
        .ignoresSafeArea()
        .statusBar(hidden: true)
    }

    // MARK: - 方法

    /// 预加载相邻图片
    private func preloadAdjacentImages() {
        // 预加载前一张图片
        if currentIndex > 0 {
            let prevIndex = currentIndex - 1
            let prevData = imageDatas[prevIndex]
            let prevKey = "\(baseCacheKey)_preview_\(prevIndex)"

            if ImageManager.shared.getImageFromCache(key: prevKey) == nil {
                DispatchQueue.global(qos: .utility).async {
                    // 使用普通方式加载
                    ImageManager.shared.loadImageAsync(data: prevData, key: prevKey) { _ in
                        // 更新加载状态
                        if !loadingStates[prevIndex] {
                            DispatchQueue.main.async {
                                loadingStates[prevIndex] = true
                            }
                        }
                    }
                }
            } else {
                // 已缓存，标记为已加载
                if !loadingStates[prevIndex] {
                    loadingStates[prevIndex] = true
                }
            }
        }

        // 预加载后一张图片
        if currentIndex < imageDatas.count - 1 {
            let nextIndex = currentIndex + 1
            let nextData = imageDatas[nextIndex]
            let nextKey = "\(baseCacheKey)_preview_\(nextIndex)"

            if ImageManager.shared.getImageFromCache(key: nextKey) == nil {
                DispatchQueue.global(qos: .utility).async {
                    // 使用普通方式加载
                    ImageManager.shared.loadImageAsync(data: nextData, key: nextKey) { _ in
                        // 更新加载状态
                        if !loadingStates[nextIndex] {
                            DispatchQueue.main.async {
                                loadingStates[nextIndex] = true
                            }
                        }
                    }
                }
            } else {
                // 已缓存，标记为已加载
                if !loadingStates[nextIndex] {
                    loadingStates[nextIndex] = true
                }
            }
        }
    }
}
