//
//  SubjectLiftView.swift
//  PinkLog
//
//  Created by AI Assistant on 2025/6/17.
//

import SwiftUI

struct SubjectLiftView: View {
    @Binding var image: UIImage?
    @Binding var liftedImage: UIImage?
    @Binding var stickerImage: UIImage?

    @State private var isProcessing = false
    @State private var isCompressing = false
    @State private var hasError = false
    @State private var errorMessage = ""
    @State private var selectedImageType: ImageType = .sticker
    @State private var isLongPressing = false
    @State private var hasAutoProcessed = false // 防止重复自动处理

    // 修改回调类型，传递压缩后的Data和显示用的UIImage
    var onImageProcessed: ((Data, UIImage) -> Void)?

    enum ImageType: String, CaseIterable {
        case original = "原图"
        case lifted = "抠图"
        case sticker = "贴纸"
    }
    
    var body: some View {
        VStack(spacing: 16) {
            if let currentImage = getCurrentImage() {
                ZStack {
                    // 图片显示
                    Image(uiImage: currentImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 400)
                        .cornerRadius(12)
                        .scaleEffect(isLongPressing ? 1.05 : 1.0)
                        .shadow(color: .black.opacity(isLongPressing ? 0.3 : 0.1), 
                               radius: isLongPressing ? 15 : 5, 
                               x: 0, y: isLongPressing ? 8 : 2)
                        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isLongPressing)

                    // 处理中的加载指示器
                    if isProcessing || isCompressing {
                        ZStack {
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.black.opacity(0.6))

                            VStack(spacing: 12) {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(1.2)

                                Text(isCompressing ? "正在压缩..." : "正在抠图...")
                                    .foregroundColor(.white)
                                    .font(.caption)
                            }
                        }
                    }
                }
            }
            
            // 控制按钮
            if hasProcessedImages() {
                VStack(spacing: 12) {
                    // 图片类型选择器 - 点击即应用
                    HStack(spacing: 8) {
                        ForEach(getAvailableImageTypes(), id: \.self) { imageType in
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    selectedImageType = imageType
                                    // 立即压缩并清理其他图片
                                    selectAndOptimizeImage(imageType)
                                }
                            }) {
                                Text(imageType.rawValue)
                                    .font(.caption)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(selectedImageType == imageType ? Color.blue : Color.gray.opacity(0.3))
                                    .foregroundColor(selectedImageType == imageType ? .white : .primary)
                                    .cornerRadius(6)
                            }
                        }
                    }
                }
            }
            
            // 错误信息
            if hasError {
                Text(errorMessage)
                    .font(.caption)
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
        }
        .padding()
        .onAppear {
            // 防止重复自动处理
            guard !hasAutoProcessed else { return }

            // 当有贴纸图片时，默认选择贴纸并自动应用
            if stickerImage != nil {
                selectedImageType = .sticker
                selectAndOptimizeImage(.sticker)
                hasAutoProcessed = true
            } else if liftedImage != nil {
                selectedImageType = .lifted
                selectAndOptimizeImage(.lifted)
                hasAutoProcessed = true
            }
        }
        .onChange(of: stickerImage) { _, newStickerImage in
            // 当贴纸图片更新时，自动选择并应用贴纸（仅在未自动处理过时）
            if newStickerImage != nil && !hasAutoProcessed {
                selectedImageType = .sticker
                selectAndOptimizeImage(.sticker)
                hasAutoProcessed = true
            }
        }
    }

    // MARK: - Helper Methods

    private func getCurrentImage() -> UIImage? {
        let currentImage: UIImage?
        switch selectedImageType {
        case .original:
            currentImage = image
        case .lifted:
            currentImage = liftedImage
        case .sticker:
            currentImage = stickerImage
        }

        #if DEBUG
        print("🖼️ getCurrentImage - selectedImageType: \(selectedImageType.rawValue), hasImage: \(currentImage != nil)")
        if let img = currentImage {
            print("🖼️ 图片尺寸: \(img.size)")
        }
        #endif

        return currentImage
    }

    /// 选择并优化图片：压缩选中图片，清理其他图片引用
    /// - Parameter imageType: 选择的图片类型
    private func selectAndOptimizeImage(_ imageType: ImageType) {
        guard let selectedImage = getImageForType(imageType) else {
            hasError = true
            errorMessage = "无法获取选中的图片"
            return
        }

        // 防止重复压缩
        guard !isCompressing else { return }

        // 显示压缩进度
        isCompressing = true
        hasError = false

        // 后台线程进行压缩处理
        DispatchQueue.global(qos: .userInitiated).async {
            autoreleasepool {
                // 使用ImageManager压缩图片
                let compressedData = ImageManager.shared.compressToTargetSize(
                    selectedImage,
                    maxSize: 512,
                    quality: 0.3,
                    targetBytes: 100 * 1024
                )

                DispatchQueue.main.async {
                    self.isCompressing = false

                    guard let data = compressedData else {
                        self.hasError = true
                        self.errorMessage = "图片压缩失败"
                        return
                    }

                    // 创建压缩后的显示图片（避免传递大图片）
                    if let displayImage = UIImage(data: data) {
                        #if DEBUG
                        print("✅ 压缩成功 - 类型: \(imageType.rawValue), 数据大小: \(data.count) bytes, 图片尺寸: \(displayImage.size)")
                        #endif

                        // 🔥 先更新当前选择的图片为压缩后的小图片
                        switch imageType {
                        case .original:
                            self.image = displayImage
                        case .lifted:
                            self.liftedImage = displayImage
                        case .sticker:
                            self.stickerImage = displayImage
                        }

                        // 触发回调，传递压缩后的数据和小尺寸的显示图片
                        self.onImageProcessed?(data, displayImage)

                        // 🔥 不在这里清理其他图片，让用户可以自由切换
                        // 清理应该在用户保存后进行
                    } else {
                        self.hasError = true
                        self.errorMessage = "图片处理失败"
                        #if DEBUG
                        print("❌ 无法从压缩数据创建UIImage")
                        #endif
                    }
                }
            }
        }
    }

    /// 根据类型获取对应的图片
    /// - Parameter imageType: 图片类型
    /// - Returns: 对应的UIImage
    private func getImageForType(_ imageType: ImageType) -> UIImage? {
        switch imageType {
        case .original:
            return image
        case .lifted:
            return liftedImage
        case .sticker:
            return stickerImage
        }
    }

    /// 清理未选择的图片引用，释放内存
    /// - Parameter keepType: 要保留的图片类型
    private func clearUnselectedImages(keepType: ImageType) {
        autoreleasepool {
            switch keepType {
            case .original:
                // 保留原图，清理抠图和贴纸
                if liftedImage != nil {
                    liftedImage = nil
                }
                if stickerImage != nil {
                    stickerImage = nil
                }
            case .lifted:
                // 保留抠图，清理原图和贴纸
                if image != nil {
                    image = nil
                }
                if stickerImage != nil {
                    stickerImage = nil
                }
            case .sticker:
                // 保留贴纸，清理原图和抠图
                if image != nil {
                    image = nil
                }
                if liftedImage != nil {
                    liftedImage = nil
                }
            }

            // 强制触发内存清理
            ImageManager.shared.clearTemporaryCache()
        }
    }

    private func hasProcessedImages() -> Bool {
        return liftedImage != nil || stickerImage != nil
    }

    private func getAvailableImageTypes() -> [ImageType] {
        var types: [ImageType] = [.original]

        if liftedImage != nil {
            types.append(.lifted)
        }

        if stickerImage != nil {
            types.append(.sticker)
        }

        return types
    }
    

}

#Preview {
    SubjectLiftView(
        image: .constant(UIImage(systemName: "photo")),
        liftedImage: .constant(nil),
        stickerImage: .constant(nil)
    )
}
