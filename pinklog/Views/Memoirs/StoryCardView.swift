import SwiftUI

struct StoryCardView: View {
    let story: UsageRecord
    
    // 卡片尺寸配置
    private let cardHeight: CGFloat = 320
    private let imageHeightRatio: CGFloat = 0.65 // 图片占卡片高度的65%
    
    // 动画状态
    @State private var isPressed: Bool = false
    @State private var appearAnimation: Bool = false
    @State private var isVisibleInMemory: Bool = true
    
    // 触感引擎
    let impactGenerator = UIImpactFeedbackGenerator(style: .light)
    
    var body: some View {
        ZStack {
            // 卡片背景 - 使用磨砂玻璃效果
            RoundedRectangle(cornerRadius: AppSizes.cornerRadius * 1.2)
                .fill(Color.clear)
                .background(
                    ZStack {
                        // 磨砂背景
                        BlurEffect(style: .systemThinMaterial)
                            .cornerRadius(AppSizes.cornerRadius * 1.2)
                        
                        // 叠加微妙渐变以增加质感
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.4),
                                Color.white.opacity(0.1)
                            ],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                        .cornerRadius(AppSizes.cornerRadius * 1.2)
                        .opacity(0.5)
                    }
                )
                .overlay(
                    // 轻微边框
                    RoundedRectangle(cornerRadius: AppSizes.cornerRadius * 1.2)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.5),
                                    Color.white.opacity(0.2)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 0.5
                        )
                )
                .shadow(color: Color.black.opacity(0.07), radius: 10, x: 0, y: 4)
                .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
            
            // 卡片内容
            VStack(spacing: 0) {
                // 图片区域
                ZStack(alignment: .topTrailing) {
                    // 故事封面图片
                    if let imageData = story.getStoryCoverImageData() {
                        AsyncImageView(
                            imageData: imageData,
                            cacheKey: "story_card_\(story.id?.uuidString ?? UUID().uuidString)",
                            loadThumbnail: true,
                            contentMode: .fill
                        )
                        .frame(height: cardHeight * imageHeightRatio)
                        .clipShape(RoundedRectangle(cornerRadius: AppSizes.cornerRadius))
                        .overlay(
                            // 微妙的渐变遮罩，增强视觉层次感
                            LinearGradient(
                                colors: [
                                    Color.black.opacity(0.0),
                                    Color.black.opacity(0.2)
                                ],
                                startPoint: .top,
                                endPoint: .bottom
                            )
                            .clipShape(RoundedRectangle(cornerRadius: AppSizes.cornerRadius))
                        )
                        .padding(.horizontal, 12)
                        .padding(.top, 12)
                    } else {
                        // 没有图片时显示精美默认图标
                        ZStack {
                            RoundedRectangle(cornerRadius: AppSizes.cornerRadius)
                                .fill(
                                    LinearGradient(
                                        colors: [
                                            Color(red: 0.95, green: 0.95, blue: 0.97),
                                            Color(red: 0.9, green: 0.9, blue: 0.95)
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .overlay(
                                    RoundedRectangle(cornerRadius: AppSizes.cornerRadius)
                                        .stroke(Color.gray.opacity(0.1), lineWidth: 0.5)
                                )
                            
                            VStack(spacing: 8) {
                                if let category = story.product?.category {
                                    Image(systemName: category.icon ?? "book.closed")
                                        .font(.system(size: 36))
                                        .foregroundColor(AppColors.secondary.opacity(0.7))
                                } else {
                                    Image(systemName: "book.closed.circle")
                                        .font(.system(size: 36))
                                        .foregroundColor(AppColors.secondary.opacity(0.7))
                                }
                                
                                Text("记录的瞬间")
                                    .font(.system(size: 14, weight: .medium, design: .rounded))
                                    .foregroundColor(AppColors.secondaryText)
                            }
                        }
                        .frame(height: cardHeight * imageHeightRatio)
                        .padding(.horizontal, 12)
                        .padding(.top, 12)
                    }
                    
                    // 情感标签 - 右上角
                    EmotionTag(emotionalValue: story.emotionalValue, compact: true)
                        .padding(12)
                }
                
                // 文字区域
                VStack(alignment: .leading, spacing: 6) {
                    // 产品名称与日期
                    HStack {
                        if let productName = story.product?.name {
                            Label(productName, systemImage: "cube")
                                .font(.system(size: 13, weight: .medium))
                                .foregroundColor(AppColors.secondaryText)
                                .lineLimit(1)
                        }
                        
                        Spacer()
                        
                        Text(story.getFormattedStoryDate())
                            .font(.system(size: 13, weight: .regular))
                            .foregroundColor(AppColors.secondaryText)
                    }
                    
                    // 标题/摘要
                    Text(story.getStorySummary())
                        .font(.system(size: 16, weight: .semibold, design: .rounded))
                        .foregroundColor(AppColors.text)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                        .padding(.top, 2)
                    
                    // 完整情感标签
                    EmotionTag(emotionalValue: story.emotionalValue)
                        .padding(.top, 5)
                }
                .padding(.horizontal, 16)
                .padding(.top, 10)
                .padding(.bottom, 14)
            }
        }
        .frame(height: cardHeight)
        .scaleEffect(isPressed ? 0.97 : 1.0)
        .animation(.spring(response: 0.4, dampingFraction: 0.6), value: isPressed)
        .opacity(appearAnimation ? 1.0 : 0.0)
        .offset(y: appearAnimation ? 0 : 20)
        .onAppear {
            // 出现动画 - 加速显示
            withAnimation(.easeOut(duration: 0.3)) {
                appearAnimation = true
            }
            
            // 注册通知中心通知，用于内存管理
            NotificationCenter.default.addObserver(forName: NSNotification.Name("StoryCardViewsAppeared"), object: nil, queue: .main) { _ in
                isVisibleInMemory = true
            }
            
            NotificationCenter.default.addObserver(forName: NSNotification.Name("StoryCardViewsDisappeared"), object: nil, queue: .main) { _ in
                isVisibleInMemory = false
                // 清理内存，不在可视区域内的卡片可以释放内存
                ImageManager.shared.clearTemporaryCache()
            }
        }
        .onDisappear {
            // 移除通知观察者
            NotificationCenter.default.removeObserver(self)
        }
        // 移除onTapGesture，避免拦截NavigationLink的点击事件
        .simultaneousGesture(
            DragGesture(minimumDistance: 0)
                .onChanged { _ in
                    if !isPressed {
                        isPressed = true
                        impactGenerator.prepare()
                        impactGenerator.impactOccurred(intensity: 0.5)
                    }
                }
                .onEnded { _ in
                    isPressed = false
                }
        )
        .padding(.horizontal, AppSizes.smallPadding)
        .padding(.vertical, AppSizes.smallPadding / 2)
    }
}

// 预览提供程序
struct StoryCardPreviewProvider {
    static func createPreviewStory() -> UsageRecord {
        let context = PersistenceController.preview.container.viewContext
        
        // 创建预览用的产品
        let product = Product(context: context)
        product.id = UUID()
        product.name = "我珍藏的相机"
        product.purchaseNotes = "这是我的第一台相机"
        
        // 创建预览用的故事记录
        let storyRecord = UsageRecord(context: context)
        storyRecord.id = UUID()
        storyRecord.date = Date()
        storyRecord.title = "第一次使用它拍摄的日落，太美了"
        storyRecord.notes = "记得那天下午，我带着新相机去海边，恰好遇到了最美的日落..."
        storyRecord.product = product
        storyRecord.isStory = true
        storyRecord.emotionalValue = 5
        
        return storyRecord
    }
}

#Preview {
    ZStack {
        MemoryBackground()
        
        StoryCardView(story: StoryCardPreviewProvider.createPreviewStory())
            .frame(width: 300)
    }
} 