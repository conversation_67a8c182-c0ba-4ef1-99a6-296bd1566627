import SwiftUI
import MarkdownUI
import AVFoundation
import Combine

// 创建一个可识别的图片索引包装类型
struct ImageIndex: Identifiable {
    let id = UUID()
    let index: Int
}

struct StoryDetailView: View {
    let story: UsageRecord
    
    // u89c6u56feu72b6u6001u7ba1u7406
    @State private var selectedImageIndex: ImageIndex? = nil
    @State private var isPlayingAudio: Bool = false
    @State private var currentAudioURL: URL? = nil
    @State private var audioPlayer: AVAudioPlayer? = nil
    
    // u6027u80fdu4f18u5316u76f8u5173u72b6u6001
    @State private var imageLoaded: Bool = false
    @State private var isViewDisappearing: Bool = false
    @State private var prefetchedImages: [Int: Bool] = [:]
    @State private var viewReadyToAppear: Bool = false
    
    // u7528u4e8eu53d6u6d88u64cdu4f5cu7684u8ba2u9605u7f13u5b58
    @State private var cancellables = Set<AnyCancellable>()
    
    var body: some View {
        ZStack {
            // 背景层
            AppColors.background.ignoresSafeArea()
            
            // 内容段随动显示
            ScrollView {
                VStack(alignment: .leading, spacing: AppSizes.padding) {
                    // 关联产品信息
                    LazyView(productInfoSection)
                    
                    // 故事标题和日期
                    storyHeaderSection
                    
                    // 情感价值和满意度
                    emotionalValueSection
                    
                    // 图片区域 - 使用延迟加载
                    LazyView(imagesSection)
                    
                    // 音频区域 - 使用延迟加载
                    if story.hasAudio {
                        LazyView(audioSection)
                    }
                    
                    // 故事内容 - 使用延迟加载
                    LazyView(contentSection)
                    
                    // 关联回忆 - 使用延迟加载
                    if let memories = story.memories, !memories.isEmpty {
                        LazyView(memoriesSection(memories))
                    }
                }
                .padding()
                .opacity(viewReadyToAppear ? 1 : 0)
                .animation(.easeIn, value: viewReadyToAppear)
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        // 使用overFullScreen而不是sheet以提高性能
        .fullScreenCover(item: $selectedImageIndex) { imageIndex in
            imageFullScreenView(index: imageIndex.index)
        }
        .onAppear {
            // 预加载和预处理操作
            preloadImagesAndAssets()
            
            // 延迟稍后显示内容，开启渐入动画
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                withAnimation {
                    viewReadyToAppear = true
                }
            }
        }
        .onDisappear {
            // 设置消失标志，减少资源消耗
            isViewDisappearing = true
            
            // 停止音频播放
            stopAudio()
            
            // 清理资源
            clearResources()
        }
    }
    
    // MARK: - 关联产品信息
    private var productInfoSection: some View {
        Group {
            if let product = story.product {
                NavigationLink(destination: ProductDetailView(product: product)) {
                    HStack(spacing: AppSizes.smallPadding) {
                        // 产品图片
                        if let productImages = product.images, let imageDataArray = try? NSKeyedUnarchiver.unarchiveTopLevelObjectWithData(productImages) as? [Data], let firstImageData = imageDataArray.first {
                            AsyncImageView(
                                imageData: firstImageData,
                                cacheKey: "product_\(product.id?.uuidString ?? "")",
                                contentMode: .fill
                            )
                            .frame(width: 50, height: 50)
                            .cornerRadius(AppSizes.smallCornerRadius)
                        } else {
                            // 默认图标
                            ZStack {
                                RoundedRectangle(cornerRadius: AppSizes.smallCornerRadius)
                                    .fill(AppColors.cardBackground)
                                    .frame(width: 50, height: 50)
                                
                                Image(systemName: "cube.box")
                                    .font(.title2)
                                    .foregroundColor(AppColors.secondary)
                            }
                        }
                        
                        // 产品名称
                        VStack(alignment: .leading) {
                            Text(product.name ?? "未命名产品")
                                .font(AppFonts.headline)
                                .foregroundColor(AppColors.text)
                            
                            Text("查看产品详情")
                                .font(AppFonts.caption)
                                .foregroundColor(AppColors.primary)
                        }
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(AppColors.secondaryText)
                            .font(.caption)
                    }
                    .padding()
                    .background(AppColors.cardBackground)
                    .cornerRadius(AppSizes.cornerRadius)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    // MARK: - 故事标题和日期
    private var storyHeaderSection: some View {
        VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
            if let title = story.title, !title.isEmpty {
                Text(title)
                    .font(AppFonts.title)
                    .foregroundColor(AppColors.text)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            Text("记录于 \(story.getFormattedStoryDate(showRelative: false))")
                .font(AppFonts.caption)
                .foregroundColor(AppColors.secondaryText)
        }
    }
    
    // MARK: - 情感价值和满意度
    private var emotionalValueSection: some View {
        HStack(spacing: AppSizes.padding) {
            // 情感价值
            VStack(alignment: .leading, spacing: 4) {
                Text("情感价值")
                    .font(AppFonts.caption)
                    .foregroundColor(AppColors.secondaryText)
                
                HStack(spacing: 2) {
                    ForEach(1...5, id: \.self) { index in
                        Image(systemName: index <= story.emotionalValue ? "heart.fill" : "heart")
                            .foregroundColor(index <= story.emotionalValue ? AppColors.primary : AppColors.secondaryText)
                    }
                }
            }
            
            Divider()
                .frame(height: 30)
            
            // 满意度
            VStack(alignment: .leading, spacing: 4) {
                Text("满意度")
                    .font(AppFonts.caption)
                    .foregroundColor(AppColors.secondaryText)
                
                HStack(spacing: 2) {
                    ForEach(1...5, id: \.self) { index in
                        Image(systemName: index <= story.satisfaction ? "star.fill" : "star")
                            .foregroundColor(index <= story.satisfaction ? .yellow : AppColors.secondaryText)
                    }
                }
            }
        }
        .padding()
        .background(AppColors.cardBackground)
        .cornerRadius(AppSizes.cornerRadius)
    }
    
    // MARK: - 图片区域
    private var imagesSection: some View {
        let images = story.getStoryImages()
        
        return Group {
            if !images.isEmpty {
                VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
                    Text("图片记录")
                        .font(AppFonts.headline)
                        .foregroundColor(AppColors.text)
                    
                    ForEach(0..<images.count, id: \.self) { index in
                        Button(action: {
                            selectedImageIndex = ImageIndex(index: index)
                        }) {
                            VStack(alignment: .trailing, spacing: 4) {
                                AsyncImageView(
                                    imageData: images[index],
                                    cacheKey: "story_\(story.id?.uuidString ?? "")_image_\(index)",
                                    loadThumbnail: true,
                                    contentMode: .fill
                                )
                                .cornerRadius(AppSizes.smallCornerRadius)
                                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                                
                                Text("图 \(index + 1)/\(images.count)")
                                    .font(AppFonts.caption)
                                    .foregroundColor(AppColors.secondaryText)
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
        }
    }
    
    // MARK: - 音频区域
    private var audioSection: some View {
        let audioURLs = story.getStoryAudioURLs()
        
        return VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
            Text("语音记录")
                .font(AppFonts.headline)
                .foregroundColor(AppColors.text)
            
            ForEach(0..<audioURLs.count, id: \.self) { index in
                let url = audioURLs[index]
                let isPlaying = isPlayingAudio && currentAudioURL == url
                
                HStack {
                    Button(action: {
                        toggleAudio(url: url)
                    }) {
                        Image(systemName: isPlaying ? "pause.circle.fill" : "play.circle.fill")
                            .font(.system(size: 30))
                            .foregroundColor(AppColors.primary)
                    }
                    
                    VStack(alignment: .leading) {
                        Text("录音 \(index + 1)")
                            .font(AppFonts.subheadline)
                            .foregroundColor(AppColors.text)
                        
                        Text(url.lastPathComponent)
                            .font(AppFonts.caption)
                            .foregroundColor(AppColors.secondaryText)
                            .lineLimit(1)
                    }
                    
                    Spacer()
                }
                .padding()
                .background(AppColors.cardBackground)
                .cornerRadius(AppSizes.cornerRadius)
            }
        }
    }
    
    // MARK: - 故事内容
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
            if let notes = story.notes, !notes.isEmpty {
                Text("故事内容")
                    .font(AppFonts.headline)
                    .foregroundColor(AppColors.text)
                
                Markdown(notes)
                    .markdownTheme(.gitHub)
                    .padding()
                    .background(AppColors.cardBackground)
                    .cornerRadius(AppSizes.cornerRadius)
            }
        }
    }
    
    // MARK: - 关联回忆
    private func memoriesSection(_ memories: String) -> some View {
        VStack(alignment: .leading, spacing: AppSizes.smallPadding) {
            Text("关联回忆")
                .font(AppFonts.headline)
                .foregroundColor(AppColors.text)
            
            Text(memories)
                .font(AppFonts.body)
                .foregroundColor(AppColors.text)
                .padding()
                .background(AppColors.cardBackground)
                .cornerRadius(AppSizes.cornerRadius)
        }
    }
    
    // MARK: - 全屏图片视图 - 优化返回交互
    private func imageFullScreenView(index: Int) -> some View {
        let images = story.getStoryImages()
        
        return ZStack {
            Color.black.ignoresSafeArea()
            
            if index < images.count {
                VStack {
                    OptimizedImageView(
                         imageData: images[index],
                         cacheKey: "story_\(story.id?.uuidString ?? "")_image_\(index)_fullscreen",
                         contentMode: .fit
                     )
                    
                    Text("图 \(index + 1)/\(images.count)")
                        .foregroundColor(.white)
                        .padding(.bottom)
                }
            }
            
            VStack {
                HStack {
                    Spacer()
                    
                    Button(action: {
                        // 先预处理转场交互，返回前先清理资源
                        DispatchQueue.main.async {
                            // 异步释放资源，减少返回时的阻塞
                            ImageManager.shared.clearTemporaryCache()
                            selectedImageIndex = nil
                        }
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.white)
                            .padding()
                    }
                }
                
                Spacer()
            }
        }
        .onAppear {
            // 预加载当前图片相邻图片
            let nextIndex = min(index + 1, images.count - 1)
            if nextIndex != index, nextIndex < images.count {
                if ImageManager.shared.getImageFromCache(key: "story_\(story.id?.uuidString ?? "")_image_\(nextIndex)_fullscreen") == nil {
                    ImageManager.shared.loadThumbnailAsync(data: images[nextIndex], key: "story_\(story.id?.uuidString ?? "")_image_\(nextIndex)_fullscreen") { _ in }
                }
            }
        }
    }
    
    // MARK: - 资源管理
    
    /// 预加载图片和其他资源，提高浏览体验
    private func preloadImagesAndAssets() {
        // 预加载第一张图片的缩略图
        let images = story.getStoryImages()
        if !images.isEmpty {
            // 先检查缓存中是否有图片
            let cacheKey = "story_\(story.id?.uuidString ?? "")_image_0"
            if ImageManager.shared.getImageFromCache(key: cacheKey) == nil {
                // 异步预加载缩略图
                ImageManager.shared.loadThumbnailAsync(data: images[0], key: cacheKey) { _ in }
                
                // 标记为已预加载
                prefetchedImages[0] = true
            }
            
            // 如果有音频，预准备但不加载
            if story.hasAudio {
                let audioURLs = story.getStoryAudioURLs()
                if !audioURLs.isEmpty {
                    // 仅做简单检查，不实际加载
                    currentAudioURL = nil
                }
            }
        }
    }
    
    /// 清理资源，优化内存使用
    private func clearResources() {
        // 清理图片缓存
        // 使用低优先级队列，避免阻塞返回操作
        DispatchQueue.global(qos: .background).async {
            autoreleasepool {
                // 取消所有订阅
                self.cancellables.removeAll()
                
                // 这里不直接清除所有缓存，只减少缓存数量限制
                ImageManager.shared.clearTemporaryCache()
            }
        }
    }
    
    // MARK: - 音频控制函数
    private func toggleAudio(url: URL) {
        if isPlayingAudio && currentAudioURL == url {
            // 如果当前正在播放这个音频，则停止播放
            stopAudio()
        } else {
            // 如果正在播放其他音频，先停止
            stopAudio()
            
            // 开始播放新的音频
            do {
                let player = try AVAudioPlayer(contentsOf: url)
                player.delegate = AVPlayerDelegate(onFinish: {
                    self.isPlayingAudio = false
                    self.currentAudioURL = nil
                })
                player.play()
                
                self.audioPlayer = player
                self.isPlayingAudio = true
                self.currentAudioURL = url
            } catch {
                print("无法播放音频: \(error)")
            }
        }
    }
    
    private func stopAudio() {
        audioPlayer?.stop()
        audioPlayer = nil
        isPlayingAudio = false
        currentAudioURL = nil
    }
}

// MARK: - AVPlayer代理
class AVPlayerDelegate: NSObject, AVAudioPlayerDelegate {
    private let onFinish: () -> Void
    
    init(onFinish: @escaping () -> Void) {
        self.onFinish = onFinish
    }
    
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        onFinish()
    }
}

// MARK: - 预览提供程序
struct StoryDetailPreviewProvider {
    static func createPreviewStory() -> UsageRecord {
        let context = PersistenceController.preview.container.viewContext
        
        // 创建预览用的产品
        let product = Product(context: context)
        product.id = UUID()
        product.name = "我珍藏的相机"
        product.purchaseNotes = "这是我的第一台相机"
        
        // 创建预览用的故事记录
        let storyRecord = UsageRecord(context: context)
        storyRecord.id = UUID()
        storyRecord.date = Date()
        storyRecord.title = "第一次使用它拍摄的日落，太美了"
        storyRecord.notes = """
        # 难忘的日落瞬间
        
        记得那天下午，我带着新相机去海边，**恰好**遇到了最美的日落...
        
        海天一色的景象让我感到无比平静，这大概就是:
        
        * 生活的意义
        * 珍贵的记忆
        * 值得收藏的瞬间
        
        从那以后，这台相机就成了我最珍视的物品之一。
        """
        storyRecord.product = product
        storyRecord.isStory = true
        storyRecord.emotionalValue = 5
        storyRecord.satisfaction = 4
        storyRecord.memories = "这让我想起了童年时跟着爸爸一起拍照的日子，那时候还用的是胶片相机..."
        
        return storyRecord
    }
}

// MARK: - 预览
#Preview {
    NavigationStack {
        StoryDetailView(story: StoryDetailPreviewProvider.createPreviewStory())
    }
} 