import SwiftUI
import Charts

// 本地组件
// 确保可以访问ChartPoint和ChartDataAdapter

struct ProductDetailView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var productViewModel: ProductViewModel
    @EnvironmentObject var usageViewModel: UsageViewModel
    @EnvironmentObject var themeManager: ThemeManager
    // 移除了 loanRecordViewModel

    let product: Product

    @State private var showingEditSheet = false
    @State private var showingDeleteAlert = false
    @State private var showingAddUsageSheet = false
    @State private var showingAddExpenseSheet = false
    @State private var showingAddTransferSheet = false
    @State private var showingRelatedProductsSheet = false
    @State private var showingEditProductSheet = false
    @State private var showingRelationshipGraph = false
    @State private var selectedTab = 0
    @State private var selectedUsageRecord: UsageRecord?

    @StateObject private var timelineViewModel = TimelineViewModel(
        usageRepository: UsageRecordRepository(context: PersistenceController.shared.container.viewContext),
        expenseRepository: RelatedExpenseRepository(context: PersistenceController.shared.container.viewContext)
    )

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 产品基本信息
                productInfoSection

                // 值度指数
                worthIndexSection

                // 选项卡
                tabSelectionView

                // 选项卡内容
                tabContentView
            }
            .padding()
        }
        .navigationTitle(product.name ?? "产品详情")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Menu {
                    Button(action: {
                        showingEditSheet = true
                    }) {
                        Label("编辑产品", systemImage: "pencil")
                    }

                    Button(action: {
                        showingAddUsageSheet = true
                    }) {
                        Label("记录使用", systemImage: "checkmark.circle")
                    }

                    Button(action: {
                        showingAddExpenseSheet = true
                    }) {
                        Label("添加费用", systemImage: "creditcard")
                    }

                    Button(action: {
                        showingAddTransferSheet = true
                    }) {
                        Label("转让", systemImage: "arrow.triangle.swap")
                    }

                    Button(action: {
                        showingRelatedProductsSheet = true
                    }) {
                        Label("关联产品", systemImage: "link")
                    }

                    Button(action: {
                        showingRelationshipGraph = true
                    }) {
                        Label("产品关系图", systemImage: "chart.bar.xaxis")
                    }

                    Button(role: .destructive, action: {
                        showingDeleteAlert = true
                    }) {
                        Label("删除产品", systemImage: "trash")
                    }
                } label: {
                    Image(systemName: "ellipsis.circle")
                }
            }
        }
        .sheet(isPresented: $showingEditSheet) {
            EditProductView(product: product)
        }
        .sheet(isPresented: $showingAddUsageSheet) {
            AddUsageRecordView(product: product)
        }
        .sheet(isPresented: $showingAddExpenseSheet) {
            AddExpenseView(product: product)
        }
        .sheet(isPresented: $showingAddTransferSheet) {
            AddTransferRecordView(product: product)
        }
        .sheet(item: $selectedUsageRecord) { record in
            UsageRecordDetailView(usageRecord: record, product: product)
                .environmentObject(themeManager)
                .environmentObject(usageViewModel)
        }
        .sheet(isPresented: $showingRelatedProductsSheet) {
            EnhancedRelatedProductsView(product: product, context: product.managedObjectContext!)
                .environmentObject(productViewModel)
                .environmentObject(themeManager)
                .environmentObject(usageViewModel)
                .environmentObject(usageViewModel)
        }
        .sheet(isPresented: $showingRelationshipGraph) {
            ProductRelationshipGraphView(product: product, context: product.managedObjectContext!)
                .environmentObject(themeManager)
                .environmentObject(usageViewModel)
        }
        .alert(isPresented: $showingDeleteAlert) {
            Alert(
                title: Text("删除产品"),
                message: Text("确定要删除\"\(product.name ?? "此产品")\"吗？此操作无法撤销。"),
                primaryButton: .destructive(Text("删除")) {
                    productViewModel.deleteProduct(product)
                    presentationMode.wrappedValue.dismiss()
                },
                secondaryButton: .cancel()
            )
        }
        .onAppear {
            usageViewModel.setCurrentProduct(product)
            // 移除了 loanRecordViewModel.setCurrentProduct(product)
        }
    }

    // 产品基本信息
    private var productInfoSection: some View {
        VStack(spacing: 16) {
            // 产品图片 - 使用优化后的AsyncImageView，保持宽高比
            if let imageData = product.images, let productId = product.id?.uuidString {
                AsyncImageView(
                    imageData: imageData,
                    cacheKey: "product_\(productId)",
                    contentMode: .fit
                )
                .frame(maxHeight: 300)
                .cornerRadius(12)
                .clipped()
            } else {
                Rectangle()
                    .fill(Color.gray.opacity(0.2))
                    .frame(height: 200)
                    .cornerRadius(12)
                    .overlay(
                        Image(systemName: "photo")
                            .font(.largeTitle)
                            .foregroundColor(.gray)
                    )
            }

            // 产品信息
            VStack(alignment: .leading, spacing: 12) {
                // 名称和品牌
                VStack(alignment: .leading, spacing: 4) {
                    Text(product.name ?? "未命名产品")
                        .font(.title2)
                        .fontWeight(.bold)

                    if let brand = product.brand, !brand.isEmpty {
                        Text(brand + (product.model != nil ? " · \(product.model!)" : ""))
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                }

                // 基本信息
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("购买价格")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("¥\(Int(product.price))")
                            .font(.headline)
                    }

                    Spacer()

                    VStack(alignment: .leading, spacing: 4) {
                        Text("购买日期")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(product.purchaseDate?.formatted(date: .abbreviated, time: .omitted) ?? "未知")
                            .font(.headline)
                    }

                    Spacer()

                    VStack(alignment: .leading, spacing: 4) {
                        Text("类别")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(product.category?.name ?? "未分类")
                            .font(.headline)
                    }
                }

                // 关键日期
                if product.expiryDate != nil || product.warrantyEndDate != nil {
                    HStack {
                        if let expiryDate = product.expiryDate {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("有效期至")
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Text(expiryDate.formatted(date: .abbreviated, time: .omitted))
                                    .font(.headline)
                                    .foregroundColor(product.isNearExpiry ? .red : .primary)
                            }

                            Spacer()
                        }

                        if let warrantyEndDate = product.warrantyEndDate {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("保修截止")
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Text(warrantyEndDate.formatted(date: .abbreviated, time: .omitted))
                                    .font(.headline)
                                    .foregroundColor(product.isNearWarrantyEnd ? .orange : .primary)
                            }
                        }
                    }
                }

                // 保修信息 (New Section)
                if let warrantyDetails = product.warrantyDetails, !warrantyDetails.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("保修摘要")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(warrantyDetails)
                            .font(.subheadline)
                    }
                    .padding(.top, 8) // Add some spacing
                }

                if let warrantyImagePath = product.warrantyImage, !warrantyImagePath.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("保修凭证")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        if let fileURL = getWarrantyFileURL(relativePath: warrantyImagePath) {
                            // Try to display as a Link if it's a common document type or image
                            // For images, a thumbnail would be better. For now, a link.
                            // Note: Direct linking to Application Support files might not work as expected
                            // without further setup (e.g., UIDocumentInteractionController or ShareLink for export).
                            // If it's an iCloud Drive URL, it should open.
                            HStack {
                                Image(systemName: "doc.text.fill") // Generic document icon
                                Link(URL(fileURLWithPath: warrantyImagePath).lastPathComponent, destination: fileURL)
                            }
                            .font(.subheadline)
                        } else {
                            Text(URL(fileURLWithPath: warrantyImagePath).lastPathComponent)
                                .font(.subheadline)
                            Text("(无法生成可访问链接)")
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                    }
                    .padding(.top, 8)
                }

                // 产品ID
                if let productId = product.id {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("产品ID")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(productId.uuidString)
                            .font(.caption)
                            .foregroundColor(product.isVirtualProduct ? themeManager.currentTheme.primaryColor : .secondary)
                            .lineLimit(1)
                            .truncationMode(.middle)
                    }
                    .padding(.top, 8)
                }

                // 标签
                if let tags = product.tags?.allObjects as? [Tag], !tags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack {
                            ForEach(tags, id: \.self) { tag in
                                Text(tag.name ?? "")
                                    .font(.caption)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(tagColor(for: tag).opacity(0.2))
                                    .foregroundColor(tagColor(for: tag))
                                    .cornerRadius(8)
                            }
                        }
                    }
                }
            }
            .padding()
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)
        }
    }

    // 值度指数
    private var worthIndexSection: some View {
        HStack(spacing: 20) {
            // 值度指数
            VStack(spacing: 8) {
                WorthIndexView(value: product.worthItIndex())

                Text(product.status.rawValue)
                    .font(.caption)
                    .foregroundColor(product.status.color)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(product.status.color.opacity(0.2))
                    .cornerRadius(8)
            }

            // 使用和成本信息
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(product.valuationMethod == "daily" ? "总服役天数" : "总使用次数")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(product.valuationMethod == "daily" ?
                             "\(Calendar.current.dateComponents([.day], from: product.purchaseDate ?? Date(), to: Date()).day ?? 0)天" :
                             "\(product.totalUsageCount)次")
                            .font(.headline)
                    }

                    Spacer()

                    VStack(alignment: .leading, spacing: 4) {
                        Text("总持有成本")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("¥\(Int(product.totalCostOfOwnership))")
                            .font(.headline)
                    }
                }

                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(product.valuationMethod == "daily" ? "单日使用成本" : "单次使用成本")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("¥\(Int(product.costPerUse))")
                            .font(.headline)
                    }

                    Spacer()

                    VStack(alignment: .leading, spacing: 4) {
                        Text("平均满意度")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("\(product.averageSatisfaction, specifier: "%.1f")/5")
                            .font(.headline)
                    }
                }

                if let _ = product.lastUsageDate, let days = product.daysSinceLastUsage {
                    HStack {
                        Image(systemName: "clock")
                            .foregroundColor(.secondary)

                        Text("\(days)天前使用")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }

    // 选项卡选择
    private var tabSelectionView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack {
                Button(action: { selectedTab = 0 }) {
                    Text("使用记录")
                        .font(.subheadline)
                        .padding(.vertical, 8)
                        .padding(.horizontal, 12)
                        .background(selectedTab == 0 ? themeManager.currentTheme.primaryColor : Color.clear)
                        .foregroundColor(selectedTab == 0 ? .white : .primary)
                        .cornerRadius(8)
                }

                Button(action: { selectedTab = 1 }) {
                    Text("费用记录")
                        .font(.subheadline)
                        .padding(.vertical, 8)
                        .padding(.horizontal, 12)
                        .background(selectedTab == 1 ? themeManager.currentTheme.primaryColor : Color.clear)
                        .foregroundColor(selectedTab == 1 ? .white : .primary)
                        .cornerRadius(8)
                }

                Button(action: { selectedTab = 2 }) {
                    Text("分析")
                        .font(.subheadline)
                        .padding(.vertical, 8)
                        .padding(.horizontal, 12)
                        .background(selectedTab == 2 ? themeManager.currentTheme.primaryColor : Color.clear)
                        .foregroundColor(selectedTab == 2 ? .white : .primary)
                        .cornerRadius(8)
                }

                Button(action: { selectedTab = 3 }) {
                    Text("关联产品")
                        .font(.subheadline)
                        .padding(.vertical, 8)
                        .padding(.horizontal, 12)
                        .background(selectedTab == 3 ? themeManager.currentTheme.primaryColor : Color.clear)
                        .foregroundColor(selectedTab == 3 ? .white : .primary)
                        .cornerRadius(8)
                }

                Button(action: { selectedTab = 4 }) {
                    Text("时间线")
                        .font(.subheadline)
                        .padding(.vertical, 8)
                        .padding(.horizontal, 12)
                        .background(selectedTab == 4 ? themeManager.currentTheme.primaryColor : Color.clear)
                        .foregroundColor(selectedTab == 4 ? .white : .primary)
                        .cornerRadius(8)
                }
            }
            .padding(4)
        }
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(10)
    }

    // 选项卡内容
    @ViewBuilder
    private var tabContentView: some View {
        switch selectedTab {
        case 0:
            usageRecordsView
        case 1:
            expenseRecordsView
        case 2:
            analysisView
        case 3:
            relatedProductsView
        case 4:
            ProductTimelineView(viewModel: timelineViewModel, product: product)
                .environmentObject(themeManager)
                .environmentObject(usageViewModel)
        default:
            EmptyView()
        }
    }

    // 使用记录视图
    private var usageRecordsView: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("使用记录")
                    .font(.headline)

                Spacer()

                Button(action: {
                    showingAddUsageSheet = true
                }) {
                    Label("添加", systemImage: "plus")
                        .font(.caption)
                }
            }

            if usageViewModel.usageRecords.isEmpty {
                Text("暂无使用记录")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .background(Color(UIColor.tertiarySystemBackground))
                    .cornerRadius(12)
            } else {
                // 按时间顺序排序所有记录
                let sortedRecords = usageViewModel.usageRecords.sorted {
                    ($0.date ?? Date()) > ($1.date ?? Date())
                }

                ForEach(sortedRecords) { record in
                    NavigationLink(destination: UsageRecordDetailView(usageRecord: record, product: product)
                        .environmentObject(themeManager)
                        .environmentObject(usageViewModel)
                    ) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(record.date?.formatted(date: .abbreviated, time: .omitted) ?? "未知日期")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)

                                if record.type == .personal {
                                    // 个人使用记录显示场景
                                    HStack(spacing: 4) {
                                        // 如果是物品故事，添加特殊标记
                                        if record.isStory {
                                            Text("物品故事")
                                                .font(.caption)
                                                .foregroundColor(.white)
                                                .padding(.horizontal, 6)
                                                .padding(.vertical, 2)
                                                .background(Color.orange)
                                                .cornerRadius(4)
                                        }

                                        if let scenario = record.scenario, !scenario.isEmpty {
                                            Text(scenario)
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    }

                                    // 如果是物品故事且有标题，显示标题
                                    if record.isStory, let title = record.title, !title.isEmpty {
                                        Text(title)
                                            .font(.caption)
                                            .fontWeight(.medium)
                                            .foregroundColor(themeManager.currentTheme.primaryColor)
                                    }
                                } else {
                                    // 借出记录显示借出对象
                                    Text("借给: \(record.borrowerName ?? "未知")")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }

                                if let notes = record.notes, !notes.isEmpty {
                                    Text(notes)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .lineLimit(record.isStory ? 1 : 2) // 物品故事仅显示一行备注
                                }
                            }

                            Spacer()

                            // 根据记录类型显示不同的右侧内容
                            if record.type == .personal {
                                VStack(alignment: .trailing, spacing: 4) {
                                    // 个人使用记录显示满意度
                                    HStack(spacing: 2) {
                                        // 如果是物品故事，显示特殊图标
                                        if record.isStory {
                                            Image(systemName: "book.fill")
                                                .foregroundColor(.orange)
                                                .font(.caption)
                                        }

                                        ForEach(1...5, id: \.self) { index in
                                            Image(systemName: index <= record.satisfaction ? "star.fill" : "star")
                                                .font(.caption)
                                                .foregroundColor(index <= record.satisfaction ? .yellow : .gray)
                                        }
                                    }

                                    // 如果是物品故事，显示情感价值
                                    if record.isStory && record.emotionalValue > 0 {
                                        HStack(spacing: 2) {
                                            Text("情感价值:")
                                                .font(.caption2)
                                                .foregroundColor(.secondary)

                                            ForEach(1...Int(record.emotionalValue), id: \.self) { _ in
                                                Image(systemName: "heart.fill")
                                                    .font(.caption2)
                                                    .foregroundColor(.red)
                                            }
                                        }
                                    }

                                    // 如果是物品故事，显示多媒体内容指示器
                                    if record.isStory {
                                        HStack(spacing: 4) {
                                            if record.images != nil {
                                                Image(systemName: "photo")
                                                    .font(.caption2)
                                                    .foregroundColor(.blue)
                                            }

                                            if record.audioRecordings != nil {
                                                Image(systemName: "mic")
                                                    .font(.caption2)
                                                    .foregroundColor(.blue)
                                            }
                                        }
                                    }
                                }
                            } else {
                                // 借出记录显示借出状态
                                Text(record.loanStatus.rawValue)
                                    .font(.caption)
                                    .foregroundColor(record.loanStatus.color)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(record.loanStatus.color.opacity(0.1))
                                    .cornerRadius(8)
                            }
                        }
                        .padding()
                        .background(record.isStory ?
                                   Color.orange.opacity(0.1) :
                                   Color(UIColor.tertiarySystemBackground))
                        .cornerRadius(12)
                        // 如果是物品故事，添加一个橙色边框
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(record.isStory ? Color.orange.opacity(0.5) : Color.clear, lineWidth: 1)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }

    // 费用记录视图
    private var expenseRecordsView: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("费用记录")
                    .font(.headline)

                Spacer()

                Button(action: {
                    showingAddExpenseSheet = true
                }) {
                    Label("添加", systemImage: "plus")
                        .font(.caption)
                }
            }

            if usageViewModel.expenses.isEmpty {
                Text("暂无费用记录")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .background(Color(UIColor.tertiarySystemBackground))
                    .cornerRadius(12)
            } else {
                ForEach(usageViewModel.expenses) { expense in
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(expense.date?.formatted(date: .abbreviated, time: .omitted) ?? "未知日期")
                                .font(.subheadline)
                                .fontWeight(.semibold)

                            Text(expense.type?.name ?? "其他费用")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            if let notes = expense.notes, !notes.isEmpty {
                                Text(notes)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .lineLimit(2)
                            }
                        }

                        Spacer()

                        Text("¥\(Int(expense.amount))")
                            .font(.headline)
                            .foregroundColor(.primary)
                    }
                    .padding()
                    .background(Color(UIColor.tertiarySystemBackground))
                    .cornerRadius(12)
                }
            }
        }
    }

    // 分析视图
    private var analysisView: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("产品分析")
                .font(.headline)

            // 三曲线分析
            VStack(spacing: 16) {
                // 成本效益曲线
                let costData = ChartDataAdapter.getCostEffectivenessChartData(for: product)
                if costData.isEmpty {
                    Text("暂无足够数据生成成本效益曲线")
                        .foregroundColor(.secondary)
                        .frame(height: 150)
                        .frame(maxWidth: .infinity)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(12)
                } else {
                    ChartView(
                        data: costData,
                        title: "成本效益曲线",
                        subtitle: "随着使用次数增加，单次使用成本逐渐降低",
                        chartStyle: .line,
                        color: .green,
                        valueFormatter: ChartDataAdapter.costFormatter
                    )
                }

                // 使用率曲线
                let usageData = ChartDataAdapter.getUsageChartData(for: product)
                if usageData.isEmpty {
                    Text("暂无足够数据生成使用率曲线")
                        .foregroundColor(.secondary)
                        .frame(height: 150)
                        .frame(maxWidth: .infinity)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(12)
                } else {
                    ChartView(
                        data: usageData,
                        title: "使用率曲线",
                        subtitle: "展示产品使用频率随时间的变化",
                        chartStyle: .bar,
                        color: .blue,
                        valueFormatter: ChartDataAdapter.getUsageFormatter(for: product)
                    )
                }

                // 满意度曲线
                let satisfactionData = ChartDataAdapter.getSatisfactionChartData(for: product)
                if satisfactionData.isEmpty {
                    Text("暂无足够数据生成满意度曲线")
                        .foregroundColor(.secondary)
                        .frame(height: 150)
                        .frame(maxWidth: .infinity)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(12)
                } else {
                    ChartView(
                        data: satisfactionData,
                        title: "满意度曲线",
                        subtitle: "展示产品满意度随时间的变化",
                        chartStyle: .area,
                        color: .orange,
                        valueFormatter: ChartDataAdapter.satisfactionFormatter
                    )
                }
            }

            // 分析结论
            VStack(alignment: .leading, spacing: 8) {
                Text("分析结论")
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text("根据您的使用记录和费用情况，该产品的值度指数为\(Int(product.worthItIndex()))，属于\"\(product.status.rawValue)\"。\(product.valuationMethod == "daily" ? "单日" : "单次")使用成本为¥\(Int(product.costPerUse))，平均满意度为\(product.averageSatisfaction, specifier: "%.1f")/5。")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(UIColor.tertiarySystemBackground))
            .cornerRadius(12)
        }
    }

    // 关联产品视图
    private var relatedProductsView: some View {
        ProductDetailRelatedProductsView(product: product, context: product.managedObjectContext!)
            .environmentObject(usageViewModel)
            .padding(.horizontal, 6)  // 为关联组界面提供更好的边距显示
    }

    // Temporary helper to construct warranty file URL.
    // This logic should ideally be centralized, perhaps in Product+Extension or a FileManagerService.
    private func getWarrantyFileURL(relativePath: String) -> URL? {
        guard !relativePath.isEmpty else { return nil }
        // This replicates the logic from ProductViewModel's getWarrantyDocumentsDirectory()
        // This is a simplification and assumes files are in Application Support/WarrantyDocuments/
        // For iCloud, the base URL would be different.
        guard let appSupportDir = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first else {
            return nil
        }
        let warrantyDir = appSupportDir.appendingPathComponent("WarrantyDocuments")
        // We don't create the directory here, just construct the path.
        // The ViewModel handles creation.
        return warrantyDir.appendingPathComponent(relativePath)
    }

    // 标签颜色
    private func tagColor(for tag: Tag) -> Color {
        guard let colorName = tag.color else {
            return .gray
        }

        switch colorName {
        case "red": return .red
        case "blue": return .blue
        case "green": return .green
        case "orange": return .orange
        case "purple": return .purple
        case "pink": return .pink
        case "yellow": return .yellow
        case "teal": return .teal
        case "indigo": return .indigo
        default: return .gray
        }
    }
}

struct ProductDetailView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let product = Product(context: context)
        product.name = "MacBook Pro"
        product.brand = "Apple"
        product.model = "M1 Pro"
        product.price = 14999
        product.purchaseDate = Date()

        return NavigationView {
            ProductDetailView(product: product)
                .environmentObject(ProductViewModel(
                    repository: ProductRepository(context: context),
                    categoryRepository: CategoryRepository(context: context)
                ))
                .environmentObject(UsageViewModel(
                    usageRepository: UsageRecordRepository(context: context),
                    expenseRepository: RelatedExpenseRepository(context: context)
                ))
                .environmentObject(ThemeManager())
                .environmentObject(LoanRecordViewModel(
                    repository: LoanRecordRepository(context: context),
                    productRepository: ProductRepository(context: context)
                ))
        }
    }
}
