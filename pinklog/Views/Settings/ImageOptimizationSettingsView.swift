import SwiftUI

/// 图片优化设置视图
struct ImageOptimizationSettingsView: View {
    // MARK: - 属性
    
    /// 环境对象 - 主题管理器
    @EnvironmentObject var themeManager: ThemeManager
    

    
    // MARK: - 视图
    
    var body: some View {
        List {
            // 图片压缩设置
            Section(header: Text("图片压缩")) {
                // 图片压缩说明
                Text("应用会自动将图片压缩到512px分辨率，质量为0.3，确保文件大小小于100KB，以优化存储空间和加载性能。")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, 4)
            }
            

            
            // 图片缓存设置
            Section(header: Text("图片缓存")) {
                // 清除缓存按钮
                Button(action: {
                    ImageManager.shared.clearCache()
                }) {
                    HStack {
                        Image(systemName: "trash")
                            .foregroundColor(.red)
                        Text("清除图片缓存")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                // 缓存说明
                Text("清除缓存将释放存储空间，但可能会导致图片加载变慢。")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, 4)
            }
        }
        .listStyle(InsetGroupedListStyle())
        .navigationTitle("图片优化")

    }

}
