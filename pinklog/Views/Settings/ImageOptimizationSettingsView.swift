import SwiftUI

/// 图片优化设置视图
struct ImageOptimizationSettingsView: View {
    // MARK: - 属性
    
    /// 环境对象 - 主题管理器
    @EnvironmentObject var themeManager: ThemeManager
    
    /// 是否显示迁移确认对话框
    @State private var showingMigrationConfirmation = false
    
    /// 是否正在迁移
    @State private var isMigrating = false
    
    /// 迁移进度
    @State private var migrationProgress: Float = 0.0
    
    /// 迁移完成
    @State private var migrationCompleted = false
    
    /// 迁移错误
    @State private var migrationError: Error? = nil
    
    /// 是否显示迁移结果
    @State private var showingMigrationResult = false
    
    // MARK: - 视图
    
    var body: some View {
        List {
            // 图片压缩设置
            Section(header: Text("图片压缩")) {
                // 图片压缩说明
                Text("应用会自动将图片压缩到512px分辨率，质量为0.3，确保文件大小小于100KB，以优化存储空间和加载性能。")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, 4)
            }
            
            // 图片迁移设置
            Section(header: Text("图片迁移")) {
                // 迁移按钮
                Button(action: {
                    showingMigrationConfirmation = true
                }) {
                    HStack {
                        Image(systemName: "arrow.triangle.2.circlepath")
                            .foregroundColor(themeManager.currentTheme.primaryColor)
                        Text("将现有图片转换为渐进式JPEG")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .disabled(isMigrating)
                
                // 迁移说明
                Text("此操作将把所有现有图片转换为渐进式JPEG格式，可能需要一些时间。转换过程中请勿关闭应用。")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, 4)
                
                // 迁移进度
                if isMigrating {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("正在转换图片...")
                            .font(.subheadline)
                        
                        ProgressView(value: migrationProgress, total: 1.0)
                            .progressViewStyle(LinearProgressViewStyle())
                        
                        Text("\(Int(migrationProgress * 100))%")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top, 8)
                }
            }
            
            // 图片缓存设置
            Section(header: Text("图片缓存")) {
                // 清除缓存按钮
                Button(action: {
                    ImageManager.shared.clearCache()
                }) {
                    HStack {
                        Image(systemName: "trash")
                            .foregroundColor(.red)
                        Text("清除图片缓存")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                // 缓存说明
                Text("清除缓存将释放存储空间，但可能会导致图片加载变慢。")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, 4)
            }
        }
        .listStyle(InsetGroupedListStyle())
        .navigationTitle("图片优化")
        .onAppear {
            // 设置迁移状态回调
            ImageMigrationManager.shared.migrationStatusCallback = { progress, completed, error in
                self.migrationProgress = progress
                
                if completed {
                    self.isMigrating = false
                    self.migrationCompleted = true
                    self.migrationError = error
                    self.showingMigrationResult = true
                }
            }
        }
        .alert(isPresented: $showingMigrationConfirmation) {
            Alert(
                title: Text("确认转换"),
                message: Text("此操作将把所有现有图片转换为渐进式JPEG格式，可能需要一些时间。转换过程中请勿关闭应用。"),
                primaryButton: .default(Text("开始转换")) {
                    startMigration()
                },
                secondaryButton: .cancel(Text("取消"))
            )
        }
        .alert(isPresented: $showingMigrationResult) {
            if let error = migrationError {
                return Alert(
                    title: Text("转换失败"),
                    message: Text("转换过程中发生错误：\(error.localizedDescription)"),
                    dismissButton: .default(Text("确定"))
                )
            } else {
                return Alert(
                    title: Text("转换完成"),
                    message: Text("所有图片已成功转换为渐进式JPEG格式。"),
                    dismissButton: .default(Text("确定"))
                )
            }
        }
    }
    
    // MARK: - 方法
    
    /// 开始迁移
    private func startMigration() {
        isMigrating = true
        migrationProgress = 0.0
        migrationCompleted = false
        migrationError = nil
        
        ImageMigrationManager.shared.startMigration { success, error in
            // 迁移完成后的处理在回调中进行
        }
    }
}
